# Chat消息格式示例

## 1. 推理消息 (reasoning)
```json
{
    "type": "reasoning",
    "messageID": "688ad64cd5d56e47100e22b5",
    "text": "用户要求做一个React组件，我需要分析需求并创建相应的代码"
}
```

## 2. 文本消息 (text)
```json
{
    "type": "text",
    "messageID": "688ad64cd5d56e47100e22b5",
    "text": "我来帮你创建一个React组件"
}
```

## 3. 工具调用消息 (tool_call)

### 工具调用开始
```json
{
    "type": "tool_call",
    "messageID": "688ad64cd5d56e47100e22b5",
    "toolCall": {
        "id": "toolu_016qwYuvXGYZ4KNSZG2jLd5p",
        "name": "web-coder-npmInstall",
        "status": "start"
    }
}
```

### 工具调用等待中
```json
{
    "type": "tool_call",
    "messageID": "688ad64cd5d56e47100e22b5",
    "toolCall": {
        "id": "toolu_016qwYuvXGYZ4KNSZG2jLd5p",
        "name": "web-coder-npmInstall",
        "status": "pending"
    }
}
```

### 工具调用完成
```json
{
    "type": "tool_call",
    "messageID": "688ad64cd5d56e47100e22b5",
    "toolCall": {
        "id": "toolu_016qwYuvXGYZ4KNSZG2jLd5p",
        "name": "web-coder-npmInstall",
        "status": "complete"
    }
}
```

## 4. 工具调用流式消息 (tool_calls_stream)

### 工具调用流式开始
```json
{
    "type": "tool_calls_stream",
    "messageID": "688ad64cd5d56e47100e22b5",
    "toolCallStream": [
        {
            "id": "toolu_016qwYuvXGYZ4KNSZG2jLd5p",
            "type": "function",
            "function": {
                "name": "web-coder-npmInstall"
            }
        }
    ]
}
```

### 工具调用流式参数
```json
{
    "type": "tool_calls_stream",
    "messageID": "688ad64cd5d56e47100e22b5",
    "toolCallStream": [
        {
            "id": "toolu_016qwYuvXGYZ4KNSZG2jLd5p",
            "type": "function",
            "function": {
                "name": "web-coder-npmInstall",
                "arguments": "{\"package\": \"react\", \"version\": \"^18.0.0\"}"
            }
        }
    ]
}
```

## 使用示例

### 客户端请求
```javascript
fetch('/v1/chat/stream', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer your-jwt-token'
    },
    body: JSON.stringify({
        model: "claude-3-5-sonnet-20241022",
        prompt: "帮我创建一个React按钮组件",
        tools: [
            {
                name: "web-coder-createFile",
                description: "创建文件"
            }
        ]
    })
});
```

### 服务端SSE响应流
```
data: {"type":"reasoning","messageID":"688ad64cd5d56e47100e22b5","text":"用户要求创建React按钮组件"}

data: {"type":"text","messageID":"688ad64cd5d56e47100e22b5","text":"我来帮你创建一个React按钮组件"}

data: {"type":"tool_call","messageID":"688ad64cd5d56e47100e22b5","toolCall":{"id":"toolu_016qwYuvXGYZ4KNSZG2jLd5p","name":"web-coder-createFile","status":"start"}}

data: {"type":"tool_calls_stream","messageID":"688ad64cd5d56e47100e22b5","toolCallStream":[{"id":"toolu_016qwYuvXGYZ4KNSZG2jLd5p","type":"function","function":{"name":"web-coder-createFile","arguments":"{\"filename\":\"Button.jsx\",\"content\":\"import React from 'react';\\n\\nconst Button = ({ children, onClick, disabled = false }) => {\\n  return (\\n    <button onClick={onClick} disabled={disabled}>\\n      {children}\\n    </button>\\n  );\\n};\\n\\nexport default Button;\"}"}}]}

data: {"type":"tool_call","messageID":"688ad64cd5d56e47100e22b5","toolCall":{"id":"toolu_016qwYuvXGYZ4KNSZG2jLd5p","name":"web-coder-createFile","status":"pending"}}

data: {"type":"tool_call","messageID":"688ad64cd5d56e47100e22b5","toolCall":{"id":"toolu_016qwYuvXGYZ4KNSZG2jLd5p","name":"web-coder-createFile","status":"complete"}}

data: {"type":"text","messageID":"688ad64cd5d56e47100e22b5","text":"React按钮组件已经创建完成！"}

data: [DONE]
```

## 消息ID说明

- `messageID`: 每次chat请求生成一个唯一的UUID
- 同一次chat请求中的所有消息都使用相同的messageID
- 客户端可以通过messageID来关联同一次对话中的所有消息
- 建议使用UUID v4格式，如: `688ad64c-d5d5-6e47-100e-22b5a1b2c3d4`
