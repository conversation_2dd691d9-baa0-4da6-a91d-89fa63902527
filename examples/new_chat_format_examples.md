# New Chat Message Format - Simplified & Elegant

This document demonstrates the new unified chat message format that replaces the previous complex structure with a simple, elegant, and extensible design.

## Unified Message Structure

All messages use a single `Message` struct with optional fields:

```go
type Message struct {
    Type           string               `json:"type"`
    MessageID      string               `json:"messageID"`
    Text           string               `json:"text,omitempty"`
    ToolCall       *ToolCall            `json:"toolCall,omitempty"`
    ToolCallStream []ToolCallStreamItem `json:"toolCallStream,omitempty"`
}
```

## Message Types

### 1. Reasoning Message
Used for AI reasoning/thinking process content.

```json
{
  "type": "reasoning",
  "messageID": "550e8400-e29b-41d4-a716-************",
  "text": "Let me think about this step by step. First, I need to understand what the user is asking for..."
}
```

### 2. Text Message
Used for regular text content responses.

```json
{
  "type": "text",
  "messageID": "550e8400-e29b-41d4-a716-************",
  "text": "Hello! I'd be happy to help you with React hooks. React hooks are functions that let you use state and other React features in functional components."
}
```

### 3. Tool Call Message
Used for tool execution status updates.

```json
{
  "type": "tool_call",
  "messageID": "550e8400-e29b-41d4-a716-************",
  "toolCall": {
    "id": "call_123456",
    "name": "search",
    "status": "start"
  }
}
```

Status values:
- `start`: Tool execution started
- `pending`: Tool execution in progress
- `complete`: Tool execution completed successfully
- `error`: Tool execution failed

### 4. Tool Calls Stream Message
Used for streaming tool call arguments during execution.

```json
{
  "type": "tool_calls_stream",
  "messageID": "550e8400-e29b-41d4-a716-************",
  "toolCallStream": [
    {
      "id": "call_123456",
      "type": "function",
      "function": {
        "name": "search",
        "arguments": "{\"query\": \"React hooks tutorial\"}"
      }
    }
  ]
}
```

## Key Features

1. **Unified Structure**: Single `Message` struct handles all message types
2. **Shared Message ID**: All messages in a single chat request share the same `messageID` (UUID)
3. **Type-based Processing**: Each message type serves a specific purpose in the chat flow
4. **Tool Call Lifecycle**: Tool calls go through multiple status updates (start → pending → complete/error)
5. **Streaming Support**: Tool arguments can be built incrementally through streaming
6. **Simple & Elegant**: Clean design that's easy to extend
7. **No Over-design**: Follows the principle of keeping code simple and maintainable

## Helper Functions

The implementation provides convenient helper functions:

```go
// Create different message types
msg1 := response.NewReasoningMessage(messageID, "Thinking...")
msg2 := response.NewTextMessage(messageID, "Hello!")
msg3 := response.NewToolCallMessage(messageID, "call_123", "search", "start")
msg4 := response.NewToolCallsStreamMessage(messageID, streamItems)

// Generate message ID
messageID := response.GenerateMessageID()

// Convert to JSON
jsonStr := msg.ToJSON()
```

## Implementation Benefits

1. **Simplified Codebase**: Removed complex legacy message types
2. **Easy to Extend**: Adding new message types is straightforward
3. **Type Safety**: Go structs provide compile-time type checking
4. **JSON Efficiency**: Optional fields reduce payload size
5. **Developer Friendly**: Clear, intuitive API design

## Client Request Example

```bash
curl -N -X POST "http://localhost:8080/api/v1/chat/stream" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "model": "claude-3-5-sonnet-20241022",
    "prompt": [
      {
        "type": "text",
        "text": "Can you search for information about React hooks?"
      }
    ],
    "promptTemplates": [
      {
        "key": "agent_webpage_react",
        "attributes": {
          "language": "English"
        }
      }
    ],
    "toolsParam": {
      "auto": ["search"]
    },
    "maxTokens": 1000,
    "temperature": 0.7
  }'
```

## SSE Response Flow Example

```
data: {"type":"reasoning","messageID":"550e8400-e29b-41d4-a716-************","text":"I need to search for information about React hooks."}

data: {"type":"tool_call","messageID":"550e8400-e29b-41d4-a716-************","toolCall":{"id":"call_123456","name":"search","status":"start"}}

data: {"type":"tool_calls_stream","messageID":"550e8400-e29b-41d4-a716-************","toolCallStream":[{"id":"call_123456","type":"function","function":{"name":"search","arguments":"{\"query\": \"React hooks tutorial\"}"}}]}

data: {"type":"tool_call","messageID":"550e8400-e29b-41d4-a716-************","toolCall":{"id":"call_123456","name":"search","status":"pending"}}

data: {"type":"tool_call","messageID":"550e8400-e29b-41d4-a716-************","toolCall":{"id":"call_123456","name":"search","status":"complete"}}

data: {"type":"text","messageID":"550e8400-e29b-41d4-a716-************","text":"Based on my search, React hooks are a powerful feature introduced in React 16.8..."}

data: [DONE]
```

## Migration Notes

- **No Compatibility Layer**: Following user feedback, we removed all legacy code for a clean implementation
- **Simplified Service**: ChatService now has a single `ChatStream` method instead of multiple variants
- **Unified Processing**: All message types flow through the same processing pipeline
- **Clean Architecture**: Removed over-engineered abstractions in favor of straightforward code

This new format achieves the goal of being "简洁优雅,方便扩展" (simple, elegant, and easy to extend) as requested.
