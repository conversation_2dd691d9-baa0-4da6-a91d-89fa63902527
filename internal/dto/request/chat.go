package request

import (
	"encoding/json"
	"fmt"
)

// ChatRequest represents the chat request from client
type ChatRequest struct {
	Model           string           `json:"model" binding:"required"`
	Prompt          []PromptContent  `json:"prompt" binding:"required"`
	PromptTemplates []PromptTemplate `json:"promptTemplates,omitempty"`
	ToolsParam      *ToolsParam      `json:"toolsParam,omitempty"`
	Temperature     *float64         `json:"temperature,omitempty"`
	MaxTokens       *int             `json:"maxTokens,omitempty"`
}

// PromptContent represents a piece of content in the prompt
type PromptContent struct {
	Type string `json:"type" binding:"required"`
	Text string `json:"text,omitempty"`
}

// PromptTemplate represents a prompt template with attributes
type PromptTemplate struct {
	Key        string                 `json:"key" binding:"required"`
	Attributes map[string]interface{} `json:"attributes,omitempty"`
}

// ToolsParam represents the tools configuration
type ToolsParam struct {
	Auto []string `json:"auto,omitempty"`
}

// GetAppName implements the AppNameGetter interface
func (r *ChatRequest) GetAppName() string {
	return "web-coder-app"
}

// SetIPInfo implements the IPInfoSetter interface
func (r *ChatRequest) SetIPInfo(ipInfo *IPInfo) {
	// Chat requests don't need IP info stored in the request
	// but we implement this to satisfy the interface
}

func (r *ChatRequest) Validate() error {
	if r.Model == "" {
		return fmt.Errorf("model is required")
	}

	if len(r.Prompt) == 0 {
		return fmt.Errorf("prompt is required")
	}

	for i, content := range r.Prompt {
		if content.Type == "" {
			return fmt.Errorf("prompt[%d].type is required", i)
		}
		if content.Type == "text" && content.Text == "" {
			return fmt.Errorf("prompt[%d].text is required when type is text", i)
		}
	}

	return nil
}

// ToJSON converts the request to JSON string for logging
func (r *ChatRequest) ToJSON() string {
	data, _ := json.Marshal(r)
	return string(data)
}

// GetSystemPrompt builds the system prompt from templates
func (r *ChatRequest) GetSystemPrompt() string {
	if len(r.PromptTemplates) == 0 {
		return ""
	}

	// This is a simplified implementation
	// In a real scenario, you would have a template engine
	// that processes the templates with their attributes
	var systemPrompt string

	for _, template := range r.PromptTemplates {
		switch template.Key {
		case "agent_webpage_react":
			systemPrompt += r.buildWebpageReactPrompt(template.Attributes)
		default:
			// Handle unknown templates or log warning
		}
	}

	return systemPrompt
}

// buildWebpageReactPrompt builds the webpage react agent prompt
func (r *ChatRequest) buildWebpageReactPrompt(attributes map[string]interface{}) string {
	language := "English"
	if lang, ok := attributes["language"].(string); ok {
		language = lang
	}

	code := ""
	if c, ok := attributes["code"].(string); ok {
		code = c
	}

	prompt := fmt.Sprintf(`You are a web development assistant that helps users create and modify web pages. 

Language: %s

You have access to the following tools:
- search: Search for information on the web
- web-coder-npmInstall: Install npm packages
- web-coder-npmUninstall: Uninstall npm packages

When working with code, please think step by step and explain your reasoning.

Current code context:
%s

Please respond in %s and provide clear explanations for your actions.`, language, code, language)

	return prompt
}

// GetUserMessages converts prompt to user messages format
func (r *ChatRequest) GetUserMessages() []map[string]interface{} {
	var messages []map[string]interface{}

	if len(r.Prompt) > 0 {
		var content []map[string]interface{}
		for _, p := range r.Prompt {
			content = append(content, map[string]interface{}{
				"type": p.Type,
				"text": p.Text,
			})
		}

		messages = append(messages, map[string]interface{}{
			"role":    "user",
			"content": content,
		})
	}

	return messages
}

// GetTools returns the available tools based on the request
func (r *ChatRequest) GetTools() []map[string]interface{} {
	if r.ToolsParam == nil || len(r.ToolsParam.Auto) == 0 {
		return nil
	}

	var tools []map[string]interface{}

	for _, toolName := range r.ToolsParam.Auto {
		switch toolName {
		case "search":
			tools = append(tools, map[string]interface{}{
				"name":        "search",
				"description": "Search for information on the web",
				"input_schema": map[string]interface{}{
					"type": "object",
					"properties": map[string]interface{}{
						"query": map[string]interface{}{
							"type":        "string",
							"description": "The search query",
						},
					},
					"required": []string{"query"},
				},
			})
		case "web-coder-npmInstall":
			tools = append(tools, map[string]interface{}{
				"name":        "web-coder-npmInstall",
				"description": "Install npm packages",
				"input_schema": map[string]interface{}{
					"type": "object",
					"properties": map[string]interface{}{
						"packages": map[string]interface{}{
							"type":        "array",
							"items":       map[string]interface{}{"type": "string"},
							"description": "List of npm packages to install",
						},
						"dev": map[string]interface{}{
							"type":        "boolean",
							"description": "Whether to install as dev dependencies",
							"default":     false,
						},
					},
					"required": []string{"packages"},
				},
			})
		case "web-coder-npmUninstall":
			tools = append(tools, map[string]interface{}{
				"name":        "web-coder-npmUninstall",
				"description": "Uninstall npm packages",
				"input_schema": map[string]interface{}{
					"type": "object",
					"properties": map[string]interface{}{
						"packages": map[string]interface{}{
							"type":        "array",
							"items":       map[string]interface{}{"type": "string"},
							"description": "List of npm packages to uninstall",
						},
					},
					"required": []string{"packages"},
				},
			})
		}
	}

	return tools
}

// FunctionCallResultRequest represents the request for function call result
type FunctionCallResultRequest struct {
	CallID  string `json:"callID" binding:"required"`
	Message string `json:"message" binding:"required"`
}

// GetAppName implements the AppNameGetter interface
func (r *FunctionCallResultRequest) GetAppName() string {
	return "web-coder-app"
}

// SetIPInfo implements the IPInfoSetter interface
func (r *FunctionCallResultRequest) SetIPInfo(ipInfo *IPInfo) {
	// Function call result requests don't need IP info stored in the request
	// but we implement this to satisfy the interface
}

// Validate validates the function call result request
func (r *FunctionCallResultRequest) Validate() error {
	if r.CallID == "" {
		return fmt.Errorf("callID is required")
	}
	if r.Message == "" {
		return fmt.Errorf("message is required")
	}
	return nil
}

// ToJSON converts the request to JSON string for logging
func (r *FunctionCallResultRequest) ToJSON() string {
	data, _ := json.Marshal(r)
	return string(data)
}
