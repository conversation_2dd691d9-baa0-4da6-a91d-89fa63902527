package request

import (
	"time"

	"web-coder-app/internal/clients/clientinfo"
)

type IPInfoSetter interface {
	SetIPInfo(ip *IPInfo)
}

type ClientInfo struct {
	clientinfo.ClientInfo
}

var (
	shanghaiLocation, _ = time.LoadLocation("Asia/Shanghai")
)

type TimeZone struct {
	TZName string `json:"tz_name" form:"tz_name"`
}

func (z TimeZone) GetLocation() *time.Location {
	if z.TZName == "" {
		return shanghaiLocation
	}
	if l, err := time.LoadLocation(z.TZName); err == nil {
		return l
	}
	return shanghaiLocation
}
