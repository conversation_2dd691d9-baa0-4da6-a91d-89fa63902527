package response

import (
	"encoding/json"
	"time"
)

// ChatStreamResponse represents a streaming response chunk
type ChatStreamResponse struct {
	ID        string                 `json:"id"`
	Type      string                 `json:"type"`
	Event     string                 `json:"event,omitempty"`
	Data      map[string]interface{} `json:"data,omitempty"`
	Timestamp int64                  `json:"timestamp"`
}

// ChatThinkingResponse represents a thinking response
type ChatThinkingResponse struct {
	Type    string `json:"type"`
	Content string `json:"content"`
}

// ChatContentResponse represents a content response
type ChatContentResponse struct {
	Type    string `json:"type"`
	Content string `json:"content"`
}

// ChatToolCallResponse represents a tool call response
type ChatToolCallResponse struct {
	Type     string                 `json:"type"`
	ToolName string                 `json:"tool_name"`
	ToolID   string                 `json:"tool_id"`
	Input    map[string]interface{} `json:"input"`
}

// ChatToolResultResponse represents a tool result response
type ChatToolResultResponse struct {
	Type    string                 `json:"type"`
	ToolID  string                 `json:"tool_id"`
	Result  map[string]interface{} `json:"result"`
	Success bool                   `json:"success"`
	Error   string                 `json:"error,omitempty"`
}

// ChatErrorResponse represents an error response
type ChatErrorResponse struct {
	Type    string `json:"type"`
	Error   string `json:"error"`
	Code    string `json:"code,omitempty"`
	Details string `json:"details,omitempty"`
}

// ChatCompleteResponse represents the completion response
type ChatCompleteResponse struct {
	Type         string                 `json:"type"`
	StopReason   string                 `json:"stop_reason"`
	Usage        *UsageInfo             `json:"usage,omitempty"`
	Metadata     map[string]interface{} `json:"metadata,omitempty"`
	FinishReason string                 `json:"finish_reason,omitempty"`
}

// UsageInfo represents token usage information
type UsageInfo struct {
	InputTokens  int `json:"input_tokens"`
	OutputTokens int `json:"output_tokens"`
	TotalTokens  int `json:"total_tokens"`
}

// NewChatStreamResponse creates a new streaming response
func NewChatStreamResponse(eventType string, data map[string]interface{}) *ChatStreamResponse {
	return &ChatStreamResponse{
		ID:        generateID(),
		Type:      "chat_stream",
		Event:     eventType,
		Data:      data,
		Timestamp: time.Now().UnixMilli(),
	}
}

// NewThinkingResponse creates a thinking response
func NewThinkingResponse(content string) *ChatStreamResponse {
	return NewChatStreamResponse("thinking", map[string]interface{}{
		"type":    "thinking",
		"content": content,
	})
}

// NewContentResponse creates a content response
func NewContentResponse(content string) *ChatStreamResponse {
	return NewChatStreamResponse("content", map[string]interface{}{
		"type":    "content",
		"content": content,
	})
}

// NewToolCallResponse creates a tool call response
func NewToolCallResponse(toolName, toolID string, input map[string]interface{}) *ChatStreamResponse {
	return NewChatStreamResponse("tool_call", map[string]interface{}{
		"type":      "tool_call",
		"tool_name": toolName,
		"tool_id":   toolID,
		"input":     input,
	})
}

// NewToolResultResponse creates a tool result response
func NewToolResultResponse(toolID string, result map[string]interface{}, success bool, errorMsg string) *ChatStreamResponse {
	data := map[string]interface{}{
		"type":    "tool_result",
		"tool_id": toolID,
		"result":  result,
		"success": success,
	}
	if errorMsg != "" {
		data["error"] = errorMsg
	}
	return NewChatStreamResponse("tool_result", data)
}

// NewErrorResponse creates an error response
func NewErrorResponse(error, code, details string) *ChatStreamResponse {
	return NewChatStreamResponse("error", map[string]interface{}{
		"type":    "error",
		"error":   error,
		"code":    code,
		"details": details,
	})
}

// NewCompleteResponse creates a completion response
func NewCompleteResponse(stopReason string, usage *UsageInfo) *ChatStreamResponse {
	data := map[string]interface{}{
		"type":        "complete",
		"stop_reason": stopReason,
	}
	if usage != nil {
		data["usage"] = usage
	}
	return NewChatStreamResponse("complete", data)
}

// ToSSEData converts the response to SSE format
func (r *ChatStreamResponse) ToSSEData() string {
	data, _ := json.Marshal(r)
	return string(data)
}

// ToSSEEvent formats the response as a Server-Sent Event
func (r *ChatStreamResponse) ToSSEEvent() string {
	return "data: " + r.ToSSEData() + "\n\n"
}

// generateID generates a unique ID for the response
func generateID() string {
	// Simple ID generation - in production you might want to use UUID
	return "chat_" + string(rune(time.Now().UnixNano()))
}

// ChatSession represents a chat session
type ChatSession struct {
	ID        string                 `json:"id"`
	UserID    int                    `json:"user_id"`
	Model     string                 `json:"model"`
	CreatedAt time.Time              `json:"created_at"`
	UpdatedAt time.Time              `json:"updated_at"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// ChatSessionMessage represents a message in the chat session
type ChatSessionMessage struct {
	ID        string                 `json:"id"`
	SessionID string                 `json:"session_id"`
	Role      string                 `json:"role"` // user, assistant, system
	Content   []MessageContent       `json:"content"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
	CreatedAt time.Time              `json:"created_at"`
}

// MessageContent represents the content of a message
type MessageContent struct {
	Type string                 `json:"type"` // text, tool_call, tool_result
	Text string                 `json:"text,omitempty"`
	Data map[string]interface{} `json:"data,omitempty"`
}

// ChatStats represents chat statistics
type ChatStats struct {
	TotalMessages   int        `json:"total_messages"`
	TotalTokens     int        `json:"total_tokens"`
	InputTokens     int        `json:"input_tokens"`
	OutputTokens    int        `json:"output_tokens"`
	ToolCalls       int        `json:"tool_calls"`
	LastMessageAt   *time.Time `json:"last_message_at,omitempty"`
	AverageResponse float64    `json:"average_response_time_ms"`
}
