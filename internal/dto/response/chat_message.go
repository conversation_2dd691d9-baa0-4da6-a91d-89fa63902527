package response

import (
	"encoding/json"

	"github.com/google/uuid"
)

// Message 统一消息结构
type Message struct {
	Type           string               `json:"type"`
	MessageID      string               `json:"messageID"`
	Text           string               `json:"text,omitempty"`
	ToolCall       *ToolCall            `json:"toolCall,omitempty"`
	ToolCallStream []ToolCallStreamItem `json:"toolCallStream,omitempty"`
}

// ToolCall 工具调用信息
type ToolCall struct {
	ID     string `json:"id"`
	Name   string `json:"name"`
	Status string `json:"status"` // start, pending, complete, error
}

// ToolCallStreamItem 工具调用流式项
type ToolCallStreamItem struct {
	ID       string                  `json:"id"`
	Type     string                  `json:"type"`
	Function *ToolCallStreamFunction `json:"function,omitempty"`
}

// ToolCallStreamFunction 工具调用流式函数信息
type ToolCallStreamFunction struct {
	Name      string `json:"name"`
	Arguments string `json:"arguments,omitempty"`
}

// NewReasoningMessage 创建推理消息
func NewReasoningMessage(messageID, text string) *Message {
	return &Message{
		Type:      "reasoning",
		MessageID: messageID,
		Text:      text,
	}
}

// NewTextMessage 创建文本消息
func NewTextMessage(messageID, text string) *Message {
	return &Message{
		Type:      "text",
		MessageID: messageID,
		Text:      text,
	}
}

// NewToolCallMessage 创建工具调用消息
func NewToolCallMessage(messageID, toolCallID, toolName, status string) *Message {
	return &Message{
		Type:      "tool_call",
		MessageID: messageID,
		ToolCall: &ToolCall{
			ID:     toolCallID,
			Name:   toolName,
			Status: status,
		},
	}
}

// NewToolCallsStreamMessage 创建工具调用流式消息
func NewToolCallsStreamMessage(messageID string, toolCallStream []ToolCallStreamItem) *Message {
	return &Message{
		Type:           "tool_calls_stream",
		MessageID:      messageID,
		ToolCallStream: toolCallStream,
	}
}

// GenerateMessageID 生成消息ID
func GenerateMessageID() string {
	return uuid.New().String()
}

// ToJSON 转换为JSON字符串
func (m *Message) ToJSON() string {
	data, _ := json.Marshal(m)
	return string(data)
}
