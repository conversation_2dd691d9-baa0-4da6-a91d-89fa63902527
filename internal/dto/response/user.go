package response

import (
	"web-coder-app/internal/data/ent"
)

type UserInfo struct {
	ID              string `json:"id"`
	UserID          int    `json:"-"`
	Email           string `json:"email"`
	Avatar          string `json:"avatar"`
	RegisterType    string `json:"register_type"`
	NickName        string `json:"nickname"`
	Phone           string `json:"phone"`
	RegisterAt      int64  `json:"register_at"`
	RegisterRegion  string `json:"register_region"`
	RegisterAppName string `json:"register_app_name"`
}

type UserLoginInfo struct {
	UserInfo
	IsInviteReward bool   `json:"is_invite_reward"`
	Token          *Token `json:"token,omitempty"`
	TestPartition  string `json:"test_partition,omitempty"`
}

type UserLoginInfoV2 struct {
	*ent.User
	Token         *Token `json:"token,omitempty"`
	TestPartition string `json:"test_partition,omitempty"`
}

type Token struct {
	Token           string `json:"token"`
	TokenID         string `json:"-"`
	ExpireAt        int64  `json:"expire_at"`
	RefreshToken    string `json:"refresh_token"`
	RefreshExpireAt int64  `json:"refresh_expire_at"`
}

//type CloudFrontSignedCookiesInterface interface {
//	GetCloudFrontSignedCookies() *filecloudfront.SignedCookies
//}
//

type UserUsageInfo struct {
	TrialUsed              int    `json:"trial_used"`         // 已使用的试用次数
	TrialTotal             int    `json:"trial_total"`        // 总试用次数
	SubscriptionUsed       int    `json:"subscription_used"`  // 已使用的订阅额度
	SubscriptionTotal      int    `json:"subscription_total"` // 总订阅额度
	Interval               string `json:"interval,omitempty"`
	TrialExpireTime        int64  `json:"trial_expire_time,omitempty"`        // 试用额度过期时间 (Unix timestamp)
	SubscriptionExpireTime int64  `json:"subscription_expire_time,omitempty"` // 订阅额度过期时间 (Unix timestamp)
}
