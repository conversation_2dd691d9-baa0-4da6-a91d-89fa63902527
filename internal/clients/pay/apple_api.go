package pay

import (
	"context"
	_ "crypto/rsa"
	"web-coder-app/pkg/ecode"

	"github.com/go-pay/gopay"
	"github.com/go-pay/gopay/apple"
)

const (
	keyID       = "H55CYFG2W2"
	issuerID    = "44da3e19-26d2-4338-975e-184ca08d6375"
	bundleID    = "com.styleTransfer.ai"
	IOSBundleID = "com.styleTransfer.ai"
	privateKey  = ``
	apiURL      = "https://api.storekit.itunes.apple.com/inApps/v1/notifications/history"
)
const (
	AppleSubscriptionStatusActive             = 1
	AppleSubscriptionStatusExpired            = 2
	AppleSubscriptionStatusBillingRetry       = 3
	AppleSubscriptionStatusBillingGracePeriod = 4
	AppleSubscriptionStatusRevoked            = 5
)

func GetAppleSubscriptionInfo(privateKey string, originalTransactionId string, isIOS bool, isLive bool) (*apple.AllSubscriptionStatusesRsp, *apple.TransactionInfo, *apple.RenewalInfo, error) {
	usedBundleID := bundleID
	if isIOS {
		usedBundleID = IOSBundleID
	}
	client, err := apple.NewClient(issuerID, usedBundleID, keyID, privateKey, isLive)
	if err != nil {
		return nil, nil, nil, err
	}

	rsp, err := client.GetAllSubscriptionStatuses(context.Background(), originalTransactionId)
	if err != nil {
		if statusErr, ok := apple.IsStatusCodeError(err); ok {
			return nil, nil, nil, statusErr
		}
		return nil, nil, nil, err
	}

	if len(rsp.Data) == 0 {
		return nil, nil, nil, ecode.NoSubscriptionData
	}

	if len(rsp.Data[0].LastTransactions) == 0 {
		return nil, nil, nil, ecode.NoTransactionData
	}

	lastTransaction := rsp.Data[0].LastTransactions[0]
	ti, _ := lastTransaction.DecodeTransactionInfo()
	//fmt.Printf("TransactionInfo:%+v", ti)

	ri, _ := lastTransaction.DecodeRenewalInfo()
	//fmt.Printf("RenewalInfo:%+v", ri)
	return rsp, ti, ri, nil
}

func LookUpOrderId(privateKey string, orderID string, isIOS bool, isLive bool) (*apple.LookUpOrderIdRsp, *apple.TransactionsItem, error) {
	usedBundleID := bundleID
	if isIOS {
		usedBundleID = IOSBundleID
	}
	client, err := apple.NewClient(issuerID, usedBundleID, keyID, privateKey, isLive)
	if err != nil {
		return nil, nil, err
	}
	rsp, err := client.LookUpOrderId(context.Background(), orderID)
	if err != nil {
		return nil, nil, err
	}

	transaction, _ := rsp.SignedTransactions[0].DecodeSignedTransaction()
	//str, _ := json.MarshalIndent(transaction, "", "  ")
	//fmt.Println("transactions:%+v", string(str))
	return rsp, transaction, nil
}

func GetTransactionInfo(privateKey string, transactionID string, isIOS bool, isLive bool) (*apple.TransactionHistoryRsp, *apple.TransactionsItem, error) {
	usedBundleID := bundleID
	if isIOS {
		usedBundleID = IOSBundleID
	}
	client, err := apple.NewClient(issuerID, usedBundleID, keyID, privateKey, isLive)
	if err != nil {
		return nil, nil, err
	}

	bm := gopay.BodyMap{
		"sort":        "DESCENDING",
		"productType": "CONSUMABLE",
	}
	res, err := client.GetTransactionHistory(context.Background(), transactionID, bm)
	if err != nil {
		return nil, nil, err
	}

	if len(res.SignedTransactions) == 0 {
		return nil, nil, ecode.NoTransactionData
	}

	transaction, _ := res.SignedTransactions[0].DecodeSignedTransaction()
	return res, transaction, nil
}
