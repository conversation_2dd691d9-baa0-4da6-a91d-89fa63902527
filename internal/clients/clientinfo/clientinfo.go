package clientinfo

import (
	"strings"

	"github.com/Sider-ai/go-pkg/clientinfo"
	"github.com/go-playground/validator/v10"
	"github.com/hashicorp/go-version"

	userSchema "web-coder-app/internal/data/ent/user"
)

type ClientInfo struct {
	AppName    string `json:"app_name" form:"app_name" binding:"required,AppNameValidation"`
	AppVersion string `json:"app_version" form:"app_version"`
}

func FromPKGClientInfo(c clientinfo.ClientInfo) ClientInfo {
	return ClientInfo{
		AppName:    c.AppName,
		AppVersion: c.AppVersion,
	}
}

func (c ClientInfo) GetAppName() userSchema.RegisterAppName {
	return userSchema.RegisterAppName(c.AppName)
}

func (c ClientInfo) GetClientType() ClientType {
	return GetClientTypeFromAppName(c.GetAppName())
}

func (c ClientInfo) GetAppVersion() (*version.Version, error) {
	return version.NewVersion(c.AppVersion)
}

// App当前版本是否大于等于指定版本
func (c ClientInfo) AppVersionGte(strVersion string) bool {
	targetV, err := version.NewVersion(strVersion)
	if err != nil {
		return false
	}
	curV, err := c.GetAppVersion()
	if err != nil {
		return false
	}
	return curV.GreaterThanOrEqual(targetV)
}

func (c ClientInfo) AppVersionLte(strVersion string) bool {
	targetV, err := version.NewVersion(strVersion)
	if err != nil {
		return false
	}
	curV, err := c.GetAppVersion()
	if err != nil {
		return false
	}
	return curV.LessThanOrEqual(targetV)
}

func AppNameValidation() (string, validator.Func) {
	return AppNameValidateKey, func(fl validator.FieldLevel) bool {
		val := fl.Field().String()
		if err := userSchema.RegisterAppNameValidator(userSchema.RegisterAppName(val)); err != nil {
			return false
		}
		return true
	}
}

func GetClientTypeFromAppName(appName userSchema.RegisterAppName) ClientType {
	s := strings.ToLower(string(appName))
	if strings.Contains(s, "mac") {
		return ClientTypeMac
	}
	if strings.Contains(s, "ios") {
		return ClientTypeIos
	}
	if strings.Contains(s, "windows") {
		return ClientTypeWin
	}
	if strings.Contains(s, "ext") || strings.Contains(s, "chrome") || strings.Contains(s, "edge") {
		return ClientTypeExt
	}
	if strings.Contains(s, "web") {
		return ClientTypeWeb
	}
	if strings.Contains(s, "android") {
		return ClientTypeAndroid
	}
	return ClientTypeExt
}
