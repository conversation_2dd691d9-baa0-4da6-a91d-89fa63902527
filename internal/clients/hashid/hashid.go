package hashid

import (
	"web-coder-app/configs"
	"web-coder-app/pkg/hashidv2"
)

type HashID struct {
	User                *hashidv2.HashID
	ShareGPT            *hashidv2.HashID
	Conversation        *hashidv2.HashID
	Embedding           *hashidv2.HashID
	ConversationMessage *hashidv2.HashID
	UserImage           *hashidv2.HashID
	OCR                 *hashidv2.HashID
	ChatShare           *hashidv2.HashID
	UserMirror          *hashidv2.HashID
	Ask                 *hashidv2.HashID
	AskAnswer           *hashidv2.HashID
	DictionaryEntry     *hashidv2.HashID
	//AD                  *hashidv2.HashID
}

func NewHashID(conf *configs.Config) *HashID {
	return &HashID{
		User:                hashidv2.New(&conf.HashID.User),
		ShareGPT:            hashidv2.New(&conf.HashID.Share),
		Conversation:        hashidv2.New(&conf.HashID.Conversation),
		Embedding:           hashidv2.New(&conf.HashID.Embedding),
		ConversationMessage: hashidv2.New(&conf.HashID.ConversationMessage),
		UserImage:           hashidv2.New(&conf.HashID.UserImage),
		ChatShare:           hashidv2.New(&conf.HashID.ChatShare),
		UserMirror:          hashidv2.New(&conf.HashID.UserMirror),
		Ask:                 hashidv2.New(&conf.HashID.Ask),
		AskAnswer:           hashidv2.New(&conf.HashID.AskAnswer),
		DictionaryEntry:     hashidv2.New(&conf.HashID.DictionaryEntry),
		//OCR:                 hashidv2.New(&conf.HashID.OCR),
		//AD:                  hashidv2.New(&conf.HashID.AD),
	}
}

func (h *HashID) GetHashID(t string) *hashidv2.HashID {
	switch t {
	case "User":
		return h.User
	case "Conversation":
		return h.Conversation
	case "ConversationMessage":
		return h.ConversationMessage
	case "ShareGPT":
		return h.ShareGPT
	case "Embedding":
		return h.Embedding
	case "UserImage":
		return h.UserImage
	case "OCR":
		return h.OCR
	case "ChatShare":
		return h.ChatShare
	case "UserMirror":
		return h.UserMirror
	case "Ask":
		return h.Ask
	case "AskAnswer":
		return h.AskAnswer
	case "DictionaryEntry":
		return h.DictionaryEntry
	default:
		return nil
	}
}
