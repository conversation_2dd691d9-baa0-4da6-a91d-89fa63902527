package service

import (
	"context"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"

	"web-coder-app/configs"
	"web-coder-app/internal/data/ent"
	"web-coder-app/internal/dto/request"
	"web-coder-app/internal/dto/response"
	"web-coder-app/pkg/ecode"
	"web-coder-app/pkg/litellm"
)

type ChatToolsService struct {
	llmClient *litellm.Client
	logger    *zap.SugaredLogger
	cfg       *configs.Config
	redis     *redis.Client
}

func NewChatToolsService(opt *Options, llmClient *litellm.Client, cfg *configs.Config) *ChatToolsService {
	return &ChatToolsService{
		llmClient: llmClient,
		logger:    opt.Log,
		cfg:       cfg,
		redis:     opt.Redis,
	}
}

// ChatStream 处理流式聊天请求
func (s *ChatToolsService) ChatStream(ctx context.Context, user *ent.User, req *request.ChatRequest) (<-chan string, error) {
	if err := req.Validate(); err != nil {
		return nil, ecode.InvalidParams.WithCause(err)
	}

	responseChan := make(chan string, 100)

	go func() {
		defer close(responseChan)
		if err := s.processStreamingChat(ctx, user, req, responseChan); err != nil {
			s.logger.Errorw("Error processing streaming chat", "error", err, "user_id", user.ID)
		}
	}()

	return responseChan, nil
}

// buildInitialMessages 构建初始消息历史
func (s *ChatToolsService) buildInitialMessages(req *request.ChatRequest) []litellm.Message {
	var messages []litellm.Message

	// 添加系统提示
	systemPrompt := req.GetSystemPrompt()
	if systemPrompt != "" {
		messages = append(messages, litellm.Message{
			Role:    "system",
			Content: systemPrompt,
		})
	}

	// 添加用户消息
	userMessages := req.GetUserMessages()
	for _, userMsg := range userMessages {
		if content, ok := userMsg["content"].([]map[string]interface{}); ok {
			var textContent string
			for _, c := range content {
				if c["type"] == "text" {
					if text, ok := c["text"].(string); ok {
						textContent += text
					}
				}
			}
			if textContent != "" {
				messages = append(messages, litellm.Message{
					Role:    "user",
					Content: textContent,
				})
			}
		}
	}

	return messages
}

// convertTools 转换工具格式
func (s *ChatToolsService) convertTools(tools []map[string]interface{}) []litellm.Tool {
	var llmTools []litellm.Tool

	for _, tool := range tools {
		if name, ok := tool["name"].(string); ok {
			if desc, ok := tool["description"].(string); ok {
				if schema, ok := tool["input_schema"].(map[string]interface{}); ok {
					llmTools = append(llmTools, litellm.Tool{
						Type: "function",
						Function: litellm.FunctionSchema{
							Name:        name,
							Description: desc,
							Parameters:  schema,
						},
					})
				}
			}
		}
	}

	return llmTools
}

// processLLMRound 处理一轮LLM对话，返回工具调用
func (s *ChatToolsService) processLLMRound(ctx context.Context, llmReq *litellm.Request, responseChan chan<- string, messageID string) ([]litellm.ToolCall, error) {
	stream, err := s.llmClient.Stream(ctx, llmReq)
	if err != nil {
		return nil, fmt.Errorf("failed to start LLM stream: %w", err)
	}
	defer stream.Close()

	var toolCalls []litellm.ToolCall

	for {
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		default:
			chunk, err := stream.Read()
			if err != nil {
				if chunk != nil && chunk.Done {
					// 流结束，返回收集到的工具调用
					return toolCalls, nil
				}
				return nil, fmt.Errorf("LLM stream error: %w", err)
			}

			if chunk.Done {
				// 流结束，返回收集到的工具调用
				return toolCalls, nil
			}

			switch chunk.Type {
			case litellm.ChunkTypeContent:
				if chunk.Content != "" {
					msg := response.NewTextMessage(messageID, chunk.Content)
					select {
					case responseChan <- msg.ToJSON():
					case <-ctx.Done():
						return nil, ctx.Err()
					}
				}

			case litellm.ChunkTypeReasoning:
				if chunk.Reasoning != nil && chunk.Reasoning.Content != "" {
					msg := response.NewReasoningMessage(messageID, chunk.Reasoning.Content)
					select {
					case responseChan <- msg.ToJSON():
					case <-ctx.Done():
						return nil, ctx.Err()
					}
				}

			case litellm.ChunkTypeToolCall:
				if len(chunk.ToolCalls) > 0 {
					for _, toolCall := range chunk.ToolCalls {
						toolCalls = append(toolCalls, toolCall)

						// 发送工具调用开始消息
						startMsg := response.NewToolCallMessage(messageID, toolCall.ID, toolCall.Function.Name, "start")
						select {
						case responseChan <- startMsg.ToJSON():
						case <-ctx.Done():
							return nil, ctx.Err()
						}

						// 发送工具调用流式消息
						streamItem := response.ToolCallStreamItem{
							ID:   toolCall.ID,
							Type: "function",
							Function: &response.ToolCallStreamFunction{
								Name:      toolCall.Function.Name,
								Arguments: toolCall.Function.Arguments,
							},
						}
						streamMsg := response.NewToolCallsStreamMessage(messageID, []response.ToolCallStreamItem{streamItem})
						select {
						case responseChan <- streamMsg.ToJSON():
						case <-ctx.Done():
							return nil, ctx.Err()
						}

						// 发送工具调用pending状态
						pendingMsg := response.NewToolCallMessage(messageID, toolCall.ID, toolCall.Function.Name, "pending")
						select {
						case responseChan <- pendingMsg.ToJSON():
						case <-ctx.Done():
							return nil, ctx.Err()
						}
					}
				}
			}
		}
	}
}

// processToolCalls 处理工具调用并更新消息历史
func (s *ChatToolsService) processToolCalls(ctx context.Context, toolCalls []litellm.ToolCall, messages *[]litellm.Message, responseChan chan<- string, messageID string) error {
	// 构建assistant消息（包含工具调用）
	assistantMessage := litellm.Message{
		Role:      "assistant",
		Content:   "", // 工具调用时content为空
		ToolCalls: toolCalls,
	}
	*messages = append(*messages, assistantMessage)

	// 处理每个工具调用
	for _, toolCall := range toolCalls {
		// 等待前端执行工具调用结果
		result, execErr := s.waitForToolResult(ctx, toolCall.ID)

		// 发送工具调用完成状态
		status := "complete"
		if execErr != nil {
			status = "error"
			result = fmt.Sprintf("Tool execution failed: %v", execErr)
			s.logger.Errorw("Tool execution failed", "tool", toolCall.Function.Name, "error", execErr)
		}

		completeMsg := response.NewToolCallMessage(messageID, toolCall.ID, toolCall.Function.Name, status)
		select {
		case responseChan <- completeMsg.ToJSON():
		case <-ctx.Done():
			return ctx.Err()
		}

		// 将工具执行结果添加到消息历史中
		toolResultMessage := litellm.Message{
			Role:       "tool",
			Content:    result,
			ToolCallID: toolCall.ID,
		}
		*messages = append(*messages, toolResultMessage)
	}

	return nil
}

func (s *ChatToolsService) processStreamingChat(ctx context.Context, user *ent.User, req *request.ChatRequest, responseChan chan<- string) error {
	messageID := response.GenerateMessageID()
	s.logger.Infow("Chat stream started", "user_id", user.ID, "message_id", messageID)

	// 维护完整的对话历史
	messages := s.buildInitialMessages(req)

	// 循环处理多轮对话，直到没有工具调用
	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
			// 构建当前轮次的请求
			llmReq := &litellm.Request{
				Model:       req.Model,
				Messages:    messages,
				MaxTokens:   req.MaxTokens,
				Temperature: req.Temperature,
				Stream:      true,
			}

			// 添加工具定义
			tools := req.GetTools()
			if len(tools) > 0 {
				llmReq.Tools = s.convertTools(tools)
			}

			// 处理当前轮次的LLM响应
			toolCalls, err := s.processLLMRound(ctx, llmReq, responseChan, messageID)
			if err != nil {
				return err
			}

			// 如果没有工具调用，对话结束
			if len(toolCalls) == 0 {
				s.logger.Infow("Chat stream completed", "user_id", user.ID, "message_id", messageID)
				return nil
			}

			// 处理工具调用并更新消息历史
			if err := s.processToolCalls(ctx, toolCalls, &messages, responseChan, messageID); err != nil {
				return err
			}
		}
	}
}

func (s *ChatToolsService) buildLiteLLMRequest(req *request.ChatRequest) *litellm.Request {
	var messages []litellm.Message
	systemPrompt := req.GetSystemPrompt()
	if systemPrompt != "" {
		messages = append(messages, litellm.Message{
			Role:    "system",
			Content: systemPrompt,
		})
	}

	userMessages := req.GetUserMessages()
	for _, userMsg := range userMessages {
		if role, ok := userMsg["role"].(string); ok {
			if content, ok := userMsg["content"].([]map[string]interface{}); ok {
				var contentText string
				for _, c := range content {
					if text, ok := c["text"].(string); ok {
						contentText += text
					}
				}
				messages = append(messages, litellm.Message{
					Role:    role,
					Content: contentText,
				})
			}
		}
	}

	llmReq := &litellm.Request{
		Model:    req.Model,
		Messages: messages,
		Stream:   true,
	}
	if req.MaxTokens != nil {
		llmReq.MaxTokens = req.MaxTokens
	}
	if req.Temperature != nil {
		llmReq.Temperature = req.Temperature
	}

	tools := req.GetTools()
	if len(tools) > 0 {
		var litellmTools []litellm.Tool
		for _, tool := range tools {
			if name, ok := tool["name"].(string); ok {
				if desc, ok := tool["description"].(string); ok {
					if schema, ok := tool["input_schema"]; ok {
						litellmTools = append(litellmTools, litellm.Tool{
							Type: "function",
							Function: litellm.FunctionSchema{
								Name:        name,
								Description: desc,
								Parameters:  schema,
							},
						})
					}
				}
			}
		}
		llmReq.Tools = litellmTools
	}

	return llmReq
}

// waitForToolResult 等待前端执行工具调用结果
func (s *ChatToolsService) waitForToolResult(ctx context.Context, callID string) (string, error) {
	redisKey := fmt.Sprintf("tool_result:%s", callID)
	timeout := 60 * time.Second
	checkInterval := 5 * time.Second

	deadline := time.Now().Add(timeout)
	ticker := time.NewTicker(checkInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return "", ctx.Err()
		case <-ticker.C:
			// 检查Redis中是否有结果
			result, err := s.redis.Get(ctx, redisKey).Result()
			if err == nil {
				// 找到结果，清理Redis并返回
				s.redis.Del(ctx, redisKey)
				s.logger.Infow("Tool result received", "callID", callID, "result", result)
				return result, nil
			}

			// 检查是否超时
			if time.Now().After(deadline) {
				s.logger.Warnw("Tool execution timeout", "callID", callID)
				return "", fmt.Errorf("tool execution timeout after %v", timeout)
			}
		}
	}
}

func (s *ChatToolsService) executeTool(ctx context.Context, toolName, arguments string) (interface{}, error) {
	switch toolName {
	case "search":
		return map[string]interface{}{
			"tool":      toolName,
			"arguments": arguments,
			"result":    "Search completed",
		}, nil
	case "web-coder-npmInstall":
		return map[string]interface{}{
			"tool":      toolName,
			"arguments": arguments,
			"result":    "NPM packages installed",
		}, nil
	case "web-coder-npmUninstall":
		return map[string]interface{}{
			"tool":      toolName,
			"arguments": arguments,
			"result":    "NPM packages uninstalled",
		}, nil
	default:
		return nil, fmt.Errorf("unknown tool: %s", toolName)
	}
}

// SaveToolResult 保存工具调用结果到Redis
func (s *ChatToolsService) SaveToolResult(ctx context.Context, redisKey, result string) error {
	// 设置5分钟过期时间
	return s.redis.Set(ctx, redisKey, result, 5*time.Minute).Err()
}
