package service

import (
	"github.com/Sider-ai/go-pkg/xoss"
	"github.com/google/wire"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"

	"web-coder-app/configs"
	"web-coder-app/internal/clients"
	"web-coder-app/internal/clients/hashid"
	"web-coder-app/internal/data"
	"web-coder-app/pkg/jwt"
	"web-coder-app/pkg/litellm"
)

var ProviderSet = wire.NewSet(
	wire.Struct(new(Options), "*"),
	NewHealthSrv,
	NewUserService,
	NewChatToolsService,
	NewSimpleChatService,
	NewXOSS,
)

type Options struct {
	Log             *zap.SugaredLogger
	HashID          *hashid.HashID
	Jwt             *jwt.JWT
	Conf            *configs.Config
	UserRepo        *data.UserRepo
	UserRequestRepo *data.UserRequestRepo
	RDS             *clients.RDS
	Redis           *redis.Client
	Xoss            xoss.Oss

	LLMClient *litellm.Client
}

func NewXOSS(config *configs.Config) xoss.Oss {
	return xoss.NewOss(&xoss.Config{
		Platform: config.OSS.Platform,
		Aws:      config.OSS.Aws,
	})
}
