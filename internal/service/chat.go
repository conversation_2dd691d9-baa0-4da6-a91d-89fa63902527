package service

import (
	"context"
	"fmt"
	"strings"

	"web-coder-app/internal/data/ent"
	"web-coder-app/internal/dto/request"
	"web-coder-app/internal/dto/response"
	"web-coder-app/pkg/litellm"

	"go.uber.org/zap"
)

type SimpleChatService struct {
	llmClient *litellm.Client
	logger    *zap.SugaredLogger
}

// NewSimpleChatService 创建简单聊天服务实例
func NewSimpleChatService(opt *Options) *SimpleChatService {
	return &SimpleChatService{
		llmClient: opt.LLMClient,
		logger:    opt.Log,
	}
}

// ChatStream 处理聊天流式请求，只返回文本和推理消息
func (s *SimpleChatService) ChatStream(ctx context.Context, user *ent.User, req *request.ChatRequest) (<-chan string, error) {
	responseChan := make(chan string, 100)

	go func() {
		defer close(responseChan)
		if err := s.processSimpleChat(ctx, user, req, responseChan); err != nil {
			s.logger.Errorw("Error processing simple chat", "error", err, "user_id", user.ID)
		}
	}()

	return responseChan, nil
}

// processSimpleChat 处理简单聊天流程
func (s *SimpleChatService) processSimpleChat(ctx context.Context, user *ent.User, req *request.ChatRequest, responseChan chan<- string) error {
	messageID := response.GenerateMessageID()
	s.logger.Infow("Simple chat stream started", "user_id", user.ID, "message_id", messageID)

	llmReq := s.buildLiteLLMRequest(req)
	stream, err := s.llmClient.Stream(ctx, llmReq)
	if err != nil {
		return fmt.Errorf("failed to start LLM stream: %w", err)
	}
	defer stream.Close()

	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
			chunk, err := stream.Read()
			if err != nil {
				if chunk != nil && chunk.Done {
					s.logger.Infow("Simple chat stream completed", "user_id", user.ID, "message_id", messageID)
					return nil
				}
				return fmt.Errorf("LLM stream error: %w", err)
			}

			if chunk.Done {
				s.logger.Infow("Simple chat stream completed", "user_id", user.ID, "message_id", messageID)
				return nil
			}

			// 只处理文本和推理消息，忽略工具调用
			switch chunk.Type {
			case litellm.ChunkTypeContent:
				if chunk.Content != "" {
					msg := response.NewTextMessage(messageID, chunk.Content)
					select {
					case responseChan <- msg.ToJSON():
					case <-ctx.Done():
						return ctx.Err()
					}
				}

			case litellm.ChunkTypeReasoning:
				if chunk.Reasoning != nil {
					var reasoningText string
					// 优先使用Content，如果没有则使用Summary
					if chunk.Reasoning.Content != "" {
						reasoningText = chunk.Reasoning.Content
					} else if chunk.Reasoning.Summary != "" {
						reasoningText = chunk.Reasoning.Summary
					}

					if reasoningText != "" {
						msg := response.NewReasoningMessage(messageID, reasoningText)
						select {
						case responseChan <- msg.ToJSON():
						case <-ctx.Done():
							return ctx.Err()
						}
					}
				}

			case litellm.ChunkTypeToolCall:
				// 简单版本忽略工具调用
				s.logger.Debugw("Ignoring tool call in simple chat", "user_id", user.ID, "message_id", messageID)
			}
		}
	}
}

// buildLiteLLMRequest 构建LiteLLM请求
func (s *SimpleChatService) buildLiteLLMRequest(req *request.ChatRequest) *litellm.Request {
	var messages []litellm.Message

	// 添加系统提示
	systemPrompt := req.GetSystemPrompt()
	if systemPrompt != "" {
		messages = append(messages, litellm.Message{
			Role:    "system",
			Content: systemPrompt,
		})
	}

	// 添加用户消息
	userMessages := req.GetUserMessages()
	for _, userMsg := range userMessages {
		if content, ok := userMsg["content"].([]map[string]interface{}); ok {
			var textContent string
			for _, c := range content {
				if c["type"] == "text" {
					if text, ok := c["text"].(string); ok {
						textContent += text
					}
				}
			}
			if textContent != "" {
				messages = append(messages, litellm.Message{
					Role:    "user",
					Content: textContent,
				})
			}
		}
	}

	llmReq := &litellm.Request{
		Model:    req.Model,
		Messages: messages,
		Stream:   true,
	}

	// 设置可选参数
	if req.MaxTokens != nil {
		llmReq.MaxTokens = req.MaxTokens
	}
	if req.Temperature != nil {
		llmReq.Temperature = req.Temperature
	}

	// 为推理模型设置推理参数
	if s.isReasoningModel(req.Model) {
		// 使用请求中的参数，如果没有则使用默认值
		if req.ReasoningEffort != "" {
			llmReq.ReasoningEffort = req.ReasoningEffort
		} else {
			llmReq.ReasoningEffort = "medium" // 默认为medium
		}

		if req.ReasoningSummary != "" {
			llmReq.ReasoningSummary = req.ReasoningSummary
		} else {
			llmReq.ReasoningSummary = "detailed" // 默认为detailed以获取更多思考内容
		}

		// 设置是否强制使用Responses API
		llmReq.UseResponsesAPI = req.UseResponsesAPI

		s.logger.Infow("Setting reasoning parameters for reasoning model",
			"model", req.Model,
			"effort", llmReq.ReasoningEffort,
			"summary", llmReq.ReasoningSummary,
			"useResponsesAPI", llmReq.UseResponsesAPI)
	}

	return llmReq
}

// isReasoningModel 检查是否为推理模型
func (s *SimpleChatService) isReasoningModel(model string) bool {
	model = strings.ToLower(model)
	return strings.HasPrefix(model, "o1") ||
		strings.HasPrefix(model, "o3") ||
		strings.HasPrefix(model, "o4") ||
		strings.Contains(model, "reasoning")
}
