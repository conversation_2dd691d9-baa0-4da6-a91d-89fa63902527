package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis_rate/v10"
	"github.com/google/wire"
	"github.com/oschwald/maxminddb-golang"
	"go.uber.org/zap"

	"web-coder-app/configs"
	"web-coder-app/internal/clients/hashid"
	"web-coder-app/internal/data"
	v1 "web-coder-app/internal/routes/v1"
	"web-coder-app/pkg/jwt"
)

var ProviderSet = wire.NewSet(
	wire.Struct(new(Options), "*"),
	wire.Struct(new(v1.Options), "*"),
	NewEngine,
	NewHttpEngine,
	v1.NewHealthRoute,
	v1.NewUserRoute,
	v1.NewChatHandler,
)

type Options struct {
	Router  *gin.Engine
	Conf    *configs.Config
	Limiter *redis_rate.Limiter
	IPDB    *maxminddb.Reader
	Jwt     *jwt.JWT
	Log     *zap.SugaredLogger

	Health          *v1.HealthRoute
	User            *v1.UserRoute
	Chat            *v1.ChatHandler
	AppLogRepo      *data.AppLogRepo
	HashID          *hashid.HashID
	UserRequestRepo *data.UserRequestRepo
}
