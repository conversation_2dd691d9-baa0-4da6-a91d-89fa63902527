package v1

import (
	"github.com/bsm/redislock"
	"github.com/go-redis/redis_rate/v10"
	"go.uber.org/zap"

	"web-coder-app/configs"
	"web-coder-app/internal/data"
	"web-coder-app/internal/service"
	"web-coder-app/pkg/jwt"
)

type Options struct {
	Limiter         *redis_rate.Limiter
	Locker          *redislock.Client
	Log             *zap.SugaredLogger
	Jwt             *jwt.JWT
	UserRepo        *data.UserRepo
	UserRequestRepo *data.UserRequestRepo
	Config          *configs.Config

	HealthSrv         *service.HealthSrv
	UserService       *service.UserService
	SimpleChatService *service.SimpleChatService
}
