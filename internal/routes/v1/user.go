package v1

import (
	"errors"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis_rate/v10"
	"go.uber.org/zap"

	"web-coder-app/configs"
	"web-coder-app/internal/data"
	"web-coder-app/internal/dto/request"
	"web-coder-app/internal/routes/common"
	"web-coder-app/internal/routes/middleware"
	"web-coder-app/internal/service"
	"web-coder-app/pkg/jwt"
)

type UserRoute struct {
	log         *zap.SugaredLogger
	limiter     *redis_rate.Limiter
	jwt         *jwt.JWT
	userRepo    *data.UserRepo
	userService *service.UserService
	configs     *configs.Config
}

func NewUserRoute(opt *Options) *UserRoute {
	return &UserRoute{
		log:         opt.Log,
		limiter:     opt.Limiter,
		jwt:         opt.Jwt,
		userService: opt.UserService,
		userRepo:    opt.UserRepo,
		configs:     opt.Config,
	}
}

func (u *UserRoute) RegisterRoute(router *gin.RouterGroup) {
	auth := middleware.TokenAuth(true, u.jwt, u.userRepo)
	authNoMust := middleware.TokenAuth(false, u.jwt, u.userRepo)

	user := router.Group("/v1/user")
	{
		user.POST("/login/device_id", authNoMust, u.loginWithDeviceID)
		user.POST("/usage", auth, u.getUserUsageInfo)
		//user.POST("/check", authNoMust, u.check)
	}
}

func (u *UserRoute) loginWithDeviceID(c *gin.Context) {
	var req request.UserDeviceReq
	if err := c.ShouldBindJSON(&req); err != nil {
		common.ParamsErrorResp(c, err)
		return
	}

	if req.DeviceID == "" {
		common.ParamsErrorResp(c, errors.New("device_id is required"))
		return
	}

	common.WrapResp(c)(u.userService.RegisterByDeviceID(c.Request.Context(), &req, common.MustGetIPInfo(c)))
}

func (u *UserRoute) getUserUsageInfo(c *gin.Context) {

}
