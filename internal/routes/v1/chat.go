package v1

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"web-coder-app/internal/data"
	"web-coder-app/internal/dto/request"
	"web-coder-app/internal/dto/response"
	"web-coder-app/internal/routes/common"
	"web-coder-app/internal/routes/middleware"
	"web-coder-app/internal/service"
	"web-coder-app/pkg/ecode"
	"web-coder-app/pkg/jwt"
)

type ChatHandler struct {
	simpleChatService *service.SimpleChatService
	logger            *zap.SugaredLogger
	jwt               *jwt.JWT
	userRepo          *data.UserRepo
}

func NewChatHandler(opt Options) *ChatHandler {
	return &ChatHandler{
		simpleChatService: opt.SimpleChatService,
		logger:            opt.Log,
		jwt:               opt.Jwt,
		userRepo:          opt.UserRepo,
	}
}

// RegisterRoute implements the Registrable interface
func (h *<PERSON>t<PERSON>and<PERSON>) RegisterRoute(r *gin.RouterGroup) {
	v1Group := r.Group("/v1")
	chatGroup := v1Group.Group("/chat")
	chatGroup.Use(middleware.TokenAuth(true, h.jwt, h.userRepo))
	{
		chatGroup.POST("/stream", h.ChatStream)
		chatGroup.POST("/function-call/result", h.FunctionCallResult)
	}
}

func (h *ChatHandler) ChatStream(c *gin.Context) {
	user := common.GetCurrentUserInfo(c)
	if user == nil {
		h.logger.Error("User not found in context")
		c.JSON(http.StatusUnauthorized, response.Response{
			Code: ecode.InvalidToken.Code,
			Msg:  "User not authenticated",
		})
		return
	}

	var req request.ChatRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Errorw("Failed to bind chat request", "error", err)
		c.JSON(http.StatusBadRequest, response.Response{
			Code: ecode.InvalidParams.Code,
			Msg:  "Invalid request format: " + err.Error(),
		})
		return
	}

	// Set up SSE headers
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
	c.Header("Access-Control-Allow-Origin", "*")
	c.Header("Access-Control-Allow-Headers", "Cache-Control")

	ctx, cancel := context.WithTimeout(c.Request.Context(), 5*time.Minute)
	defer cancel()

	responseChan, err := h.simpleChatService.ChatStream(ctx, user, &req)
	if err != nil {
		h.logger.Errorw("Failed to start chat stream", "error", err)
		c.JSON(http.StatusInternalServerError, response.Response{
			Code: ecode.InternalErr.Code,
			Msg:  "Failed to start chat stream: " + err.Error(),
		})
		return
	}

	flusher, ok := c.Writer.(http.Flusher)
	if !ok {
		h.logger.Error("Streaming unsupported")
		c.JSON(http.StatusInternalServerError, response.Response{
			Code: ecode.InternalErr.Code,
			Msg:  "Streaming not supported",
		})
		return
	}

	heartbeatTicker := time.NewTicker(10 * time.Second)
	defer heartbeatTicker.Stop()

	for {
		select {
		case messageJSON, ok := <-responseChan:
			if !ok {
				fmt.Fprintf(c.Writer, "data: [DONE]\n\n")
				flusher.Flush()
				return
			}

			fmt.Fprintf(c.Writer, "data: %s\n\n", messageJSON)
			flusher.Flush()

		case <-heartbeatTicker.C:
			fmt.Fprintf(c.Writer, "data: [PULSE]\n\n")
			flusher.Flush()

		case <-ctx.Done():
			h.logger.Infow("Chat stream context cancelled", "error", ctx.Err())
			fmt.Fprintf(c.Writer, "data: [DONE]\n\n")
			flusher.Flush()
			return

		case <-c.Request.Context().Done():
			h.logger.Info("Client disconnected from chat stream")
			return
		}
	}
}

func (h *ChatHandler) FunctionCallResult(c *gin.Context) {
	user := common.GetCurrentUserInfo(c)
	if user == nil {
		h.logger.Error("User not found in context")
		c.JSON(http.StatusUnauthorized, response.Response{
			Code: ecode.InvalidToken.Code,
			Msg:  "User not authenticated",
		})
		return
	}

	var req request.FunctionCallResultRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Errorw("Failed to bind function call result request", "error", err)
		c.JSON(http.StatusBadRequest, response.Response{
			Code: ecode.InvalidParams.Code,
			Msg:  "Invalid request format: " + err.Error(),
		})
		return
	}

	// Validate the request
	if err := req.Validate(); err != nil {
		h.logger.Errorw("Function call result request validation failed", "error", err)
		c.JSON(http.StatusBadRequest, response.Response{
			Code: ecode.InvalidParams.Code,
			Msg:  err.Error(),
		})
		return
	}

	h.logger.Infow("Received function call result (simple version - not processed)",
		"callID", req.CallID,
		"message", req.Message,
		"userID", user.ID)

	// 	// 保存结果到Redis
	// redisKey := fmt.Sprintf("tool_result:%s", req.CallID)
	// err := h.chatService.SaveToolResult(c.Request.Context(), redisKey, req.Message)
	// if err != nil {
	// 	h.logger.Errorw("Failed to save tool result to Redis", "error", err, "callID", req.CallID)
	// 	// 不返回错误，因为结果已经记录在日志中
	// }

	common.SuccessResp(c, map[string]interface{}{
		"callID":  req.CallID,
		"success": "received",
	})
}
