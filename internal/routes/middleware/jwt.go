package middleware

import (
	"fmt"
	"strings"

	"github.com/Sider-ai/go-pkg/ginutil"
	"github.com/gin-gonic/gin"

	"web-coder-app/internal/data"
	userSchema "web-coder-app/internal/data/ent/user"
	"web-coder-app/internal/routes/common"
	"web-coder-app/pkg/ecode"
	"web-coder-app/pkg/jwt"
)

func TokenAuth(mustLogin bool, jwt *jwt.JWT, userRepo *data.UserRepo) gin.HandlerFunc {
	return func(c *gin.Context) {
		token := c.GetHeader("Authorization")
		if strings.Contains(token, "Basic ") {
			token = ""
		}
		if token == "" {
			token, _ = c.<PERSON>("token")
		}
		if token == "" {
			if mustLogin {
				common.ErrorResp(c, ecode.InvalidToken.WithCause(fmt.Errorf("TokenAuth:no token")))
			}
			return
		}
		claims, err := jwt.ParseToken(token)
		if err != nil {
			if mustLogin {
				common.ErrorResp(c, err)
			}
			return
		}

		u, err := userRepo.FindByID(c.Request.Context(), claims.UserID)
		if err != nil {
			if mustLogin {
				common.ErrorResp(c, ecode.InvalidToken.WithCause(fmt.Errorf("TokenAuth:FindByID err:%s", err)))
			}
			return
		}
		if u.Blocked {
			common.ErrorResp(c, ecode.CurrentUserIsBlocked)
			return
		}
		// 设备注册时下发的token
		if claims.RegisterType == "" || claims.RegisterType == string(userSchema.RegisterTypeDevice) {
			// 设备注册的账号发生了绑定，则之前下发的token无效
			if u.RegisterType != userSchema.RegisterTypeDevice {
				if mustLogin {
					common.ErrorResp(c, ecode.DeviceIDHasBind)
				}
				return
			}
		}
		// 根据服务端 login_log 判断有效性
		//if claims.TokenID != "" {
		//	if err := userRepo.LoginIsValid(c.Request.Context(), claims.TokenID); err != nil {
		//		if mustLogin {
		//			common.ErrorResp(c, err)
		//		}
		//		return
		//	}
		//}

		c.Set(common.CurrentUserJwtClaims, claims)
		c.Set(common.CurrentUserInfoKey, u)
		ginutil.SetContextValue(c, common.RequestUserIDKey, u.ID)
	}
}
