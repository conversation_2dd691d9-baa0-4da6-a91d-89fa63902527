package middleware

import (
	"encoding/base64"
	"encoding/hex"
	"errors"

	"github.com/forgoer/openssl"
	"github.com/gin-gonic/gin"

	"web-coder-app/internal/routes/common"
)

type HeaderValidationConfig struct {
	Key              string
	HeaderName       string
	CtxKey           string
	NoAbortOnFailure bool
}

func HeaderValidationMiddleware(config HeaderValidationConfig) gin.HandlerFunc {
	contextKey := config.CtxKey
	if contextKey == "" {
		contextKey = "validated_header_" + config.HeaderName
	}
	return func(c *gin.Context) {
		encryptedValue := c.GetHeader(config.HeaderName)
		if encryptedValue == "" {
			if !config.NoAbortOnFailure {
				common.WrapResp(c)(nil, errors.New("header validation value is required"))
				return
			}
			c.Next()
			return
		}

		decoded, err := base64.StdEncoding.DecodeString(encryptedValue)
		if err != nil {
			if !config.NoAbortOnFailure {
				common.WrapResp(c)(nil, errors.New("header validation value is required"))
				return
			}
			c.Next()
			return
		}

		// Decode the hex key string into raw bytes
		keyBytes, err := hex.DecodeString(config.Key)
		if err != nil {
			// Handle error: log it or return a more specific configuration error
			// For now, returning the same generic error for simplicity, but logging is recommended
			if !config.NoAbortOnFailure {
				common.WrapResp(c)(nil, errors.New("header validation configuration error")) // Or log the specific key error
				return
			}
			c.Next()
			return
		}

		// Use the decoded key bytes for decryption
		decrypted, err := openssl.AesECBDecrypt(decoded, keyBytes, openssl.PKCS7_PADDING)
		if err != nil {
			// Consider logging the actual decryption error here for debugging
			// log.Printf("Decryption error: %v", err)
			if !config.NoAbortOnFailure {
				common.WrapResp(c)(nil, errors.New("header validation value is required")) // Decryption failed
				return
			}
			c.Next()
			return
		}

		c.Set(contextKey, string(decrypted))
		c.Next()

	}
}

func GetValidatedValue(c *gin.Context, key string) (string, bool) {
	value, exists := c.Get(key)
	if !exists {
		return "", false
	}

	strValue, ok := value.(string)
	return strValue, ok
}
