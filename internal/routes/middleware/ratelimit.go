package middleware

import (
	"fmt"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis_rate/v10"
	"github.com/samber/lo"

	"web-coder-app/internal/routes/common"
	"web-coder-app/pkg/ecode"
)

var (
	globalExclude = []string{"/v1/completion/text", "/v1/user/phone/login/captcha", "/v1/user/phone/login/verify",
		"/v1/audio/speech/key/ws", "/v1/audio/speech/key", "/v1/audio/speech/key/word_boundaries", "/v1/payment/webhook",
		"/v1/payment/apple/notification"}
)

// RateLimitWithIP 针对同一个ip进行限流
func RateLimitWithIP(limiter *redis_rate.Limiter, limit redis_rate.Limit, prefix string) gin.HandlerFunc {
	return func(c *gin.Context) {
		ip := common.GetRealIP(c)

		//if isCFIPInRange(ip) {
		//	return
		//}

		path := c.Request.URL.Path
		// internal接口不限制
		if strings.Contains(path, "/internal") {
			return
		}
		// 自身加了限制的接口，不再全局限制
		if prefix == "total" && lo.Contains(globalExclude, path) {
			return
		}

		key := fmt.Sprintf("%s:%s", prefix, ip)
		result, err := limiter.Allow(c.Request.Context(), key, limit)
		if err != nil || result.Allowed == 0 {
			common.ErrorResp(c, ecode.TooManyRequest.WithCause(fmt.Errorf("key:%s, err:%s", key, err)))
			return
		}
	}
}

// RateLimitWithUserID 针对同一个user进行限流
func RateLimitWithUserID(limiter *redis_rate.Limiter, limit redis_rate.Limit, prefix string) gin.HandlerFunc {
	return func(c *gin.Context) {
		path := c.Request.URL.Path
		// internal接口不限制
		if strings.Contains(path, "/internal") {
			return
		}
		// 自身加了限制的接口，不再全局限制
		if prefix == "global" && lo.Contains(globalExclude, path) {
			return
		}
		info := common.GetCurrentUserInfo(c)
		if info == nil {
			return
		}
		key := fmt.Sprintf("%s:%d", prefix, info.ID)
		result, err := limiter.Allow(c.Request.Context(), key, limit)
		if err != nil || result.Allowed == 0 {
			common.ErrorResp(c, ecode.TooManyRequest.WithCause(fmt.Errorf("key:%s", key)))
			return
		}
	}
}
