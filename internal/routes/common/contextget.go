package common

import (
	"context"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"

	"web-coder-app/internal/data/ent"
	"web-coder-app/internal/dto/request"
	"web-coder-app/pkg/jwt"
)

const (
	IpInfoKey            = "client-ip-info"
	CurrentUserInfoKey   = "current-user-info"
	CurrentUserJwtClaims = "current-user-jwt-claims"

	RequestTraceIDKey = "request-trace-id"
	RequestUserIDKey  = "request-user-id"
)

func MustGetIPInfo(c *gin.Context) *request.IPInfo {
	return c.MustGet(IpInfoKey).(*request.IPInfo)
}

func GetIPInfoWithCtx(ctx context.Context) *request.IPInfo {
	val := ctx.Value(IpInfoKey)
	if val == nil {
		return &request.IPInfo{
			IP:           "-",
			CountryShort: "-",
		}
	}
	return val.(*request.IPInfo)
}

func GetRequestUserID(ctx context.Context) int {
	val := ctx.Value(RequestUserIDKey)
	if val == nil {
		return 0
	}
	return val.(int)
}

func GetIPInfo(c *gin.Context) *request.IPInfo {
	val, exist := c.Get(IpInfoKey)
	if !exist {
		return nil
	}
	return val.(*request.IPInfo)
}

func GetCurrentUserInfo(c *gin.Context) *ent.User {
	val, exist := c.Get(CurrentUserInfoKey)
	if !exist {
		return nil
	}
	return val.(*ent.User)
}

func GetTraceID(ctx context.Context) string {
	val := ctx.Value(RequestTraceIDKey)
	if val == nil {
		return uuid.New().String()
	}

	return val.(string)
}

func MustGetCurrentUserInfo(c *gin.Context) *ent.User {
	return c.MustGet(CurrentUserInfoKey).(*ent.User)
}

func MustGetCurrentUserJwtClaims(c *gin.Context) *jwt.CustomClaims {
	return c.MustGet(CurrentUserJwtClaims).(*jwt.CustomClaims)
}
