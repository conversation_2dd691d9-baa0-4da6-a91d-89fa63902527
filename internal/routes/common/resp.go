package common

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"time"
	"unicode/utf8"

	"github.com/aws/aws-sdk-go/service/cloudfront/sign"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/render"
	"github.com/samber/lo"
	"go.uber.org/zap"

	"web-coder-app/internal/data"
	"web-coder-app/internal/data/ent"
	"web-coder-app/internal/data/ent/applog"
	"web-coder-app/pkg/ecode"
	"web-coder-app/pkg/kv"
	"web-coder-app/pkg/xstrings"
)

var (
	log    *zap.SugaredLogger
	appLog *data.AppLogRepo
)

var (
	logExcludeCode = []int{ecode.ClientStopCompletion.Code, ecode.CompletionUseUp.Code, ecode.ChatUseUp.Code,
		ecode.GPT4UseUp.Code, ecode.GPT4UseUpForPremium.Code, ecode.CompletionInProgress.Code,
		ecode.OpenAITimeout.Code, ecode.DeviceIDHasBind.Code, ecode.PeakIntercept.Code,
		ecode.TooManyRequest.Code,
	}
	logIncludeCode = []int{ecode.OpenAIServerBusy.Code, ecode.OpenAIExceededQuota.Code,
		ecode.NoOpenAIApiKey.Code, ecode.NoOpenAIGPT4ApiKey.Code, ecode.GetOpenAIApiKeyTimeout.Code,
		ecode.GPT4RateLimit.Code, ecode.OpenaiRateLimit.Code, ecode.ServerExceededGPT4Quota.Code,
		ecode.OpenAITimeoutWithClient.Code, ecode.OpenAIStopResponse.Code, ecode.InternalErr.Code}
)

func SetRespLog(logger *zap.SugaredLogger) {
	log = logger
}

func SetAppLogRepo(repo *data.AppLogRepo) {
	appLog = repo
}

type Resp struct {
	Code    int    `json:"code"`
	Message string `json:"msg"`
	Data    any    `json:"data"`
}

func NewResp(c *gin.Context, data interface{}, err error) (int, *Resp) {
	var (
		code     = 0
		msg      = ""
		httpCode = http.StatusOK
	)
	ec := ecode.FromError(err)
	if ec != nil {
		code = ec.Code
		msg = ec.Message
		httpCode = ec.HttpCode
	}
	if code == ecode.UnknownCode {
		msg = ecode.InternalErr.Message
		//log.Errorf("NewResp receive unknown error: %s", ec)
	}
	// 保存日志
	if err != nil && ec != nil {
		param := newSaveAppLogParam(c, err, *ec)
		go saveAppLog(param)
	}
	c.Set("response-json-code", code)
	return httpCode, &Resp{
		Code:    code,
		Message: msg,
		Data:    data,
	}
}

func NewRespWithOutC(data any, err error) *Resp {
	var (
		code = 0
		msg  = ""
	)
	ec := ecode.FromError(err)
	if ec != nil {
		code = ec.Code
		msg = ec.Message
	}
	if code == ecode.UnknownCode {
		//log.Errorw("NewRespWithOutC receive unknown error", "ec", ec)
		msg = ecode.InternalErr.Message
	}
	return &Resp{
		Code:    code,
		Message: msg,
		Data:    data,
	}
}

func ErrorResp(c *gin.Context, err error) {
	c.JSON(NewResp(c, nil, err))
	c.Abort()
}

func ParamsErrorResp(c *gin.Context, err error) {
	ErrorResp(c, ecode.NewInvalidParamsErr(TranslateErr(err)))
}

func DataResp(c *gin.Context, header http.Header, data []byte) {
	ct := header.Get("Content-Type")
	c.Render(http.StatusOK, render.Data{
		ContentType: ct,
		Data:        data,
	})
}

func SuccessResp(c *gin.Context, data any) {
	c.JSON(NewResp(c, data, nil))
}

func WrapResp(c *gin.Context) func(data any, err error) {
	return func(data any, err error) {
		if err != nil {
			ErrorResp(c, err)
		} else {
			SuccessResp(c, data)
		}
	}
}

type appLogParam struct {
	err         error
	ec          ecode.Error
	contentType string
	uid         int
	clientIP    string
	method      string
	path        string
	query       string
	body        string
	traceId     string
}

func newSaveAppLogParam(c *gin.Context, err error, ec ecode.Error) *appLogParam {
	var (
		uid         int
		clientIP    string
		body        []byte
		contentType = c.Request.Header.Get("Content-Type")
	)
	if user := GetCurrentUserInfo(c); user != nil {
		uid = user.ID
	}
	if ipInfo := GetIPInfo(c); ipInfo != nil {
		clientIP = ipInfo.IP
	}
	if strings.Contains(contentType, "application/json") {
		// 只有route层调用了 c.ShouldBindBodyWith 后这里才能读到
		if cb, ok := c.Get(gin.BodyBytesKey); ok {
			if cbb, ok := cb.([]byte); ok {
				body = cbb
			}
		}
	}
	if len(body) > 65535 {
		body = []byte("body greater than 65535")
	}
	if !utf8.Valid(body) {
		body = []byte("invalid utf8 string")
	}
	return &appLogParam{
		err:         err,
		ec:          ec,
		contentType: contentType,
		uid:         uid,
		clientIP:    clientIP,
		method:      c.Request.Method,
		path:        c.Request.URL.Path,
		query:       c.Request.URL.Query().Encode(),
		body:        xstrings.BytesToString(body),
		traceId:     GetTraceID(c),
	}
}

func saveAppLog(param *appLogParam) {
	if lo.Contains(logExcludeCode, param.ec.Code) {
		return
	}

	var level = applog.LevelERROR
	switch param.ec.Code {
	case ecode.PanicCode:
		level = applog.LevelPANIC
	case ecode.UnknownCode:
		level = applog.LevelERROR
	default:
		if lo.Contains(logIncludeCode, param.ec.Code) {
			level = applog.LevelERROR
		} else {
			level = applog.LevelWARN
		}
		// invalid code 错误只在访问 /text 接口时记录
		if param.ec.Code == ecode.InvalidToken.Code && !strings.Contains(param.path, "/text") &&
			!strings.Contains(param.path, "/data-analysis") {
			return
		}
	}
	extra := kv.New()
	if param.traceId != "" {
		extra.Put("trace_id", param.traceId)
	}
	aLog := &ent.AppLog{
		UserID:     param.uid,
		IP:         param.clientIP,
		Method:     param.method,
		Path:       param.path,
		Query:      param.query,
		ErrMsg:     param.err.Error(),
		RespErrMsg: param.ec.GetString(),
		Body:       param.body,
		Level:      level,
		Code:       param.ec.Code,
		Extra:      extra,
	}
	if _, err := appLog.Create(context.Background(), aLog); err != nil {
		log.Errorf("routes.common.saveAppLog error: %s", err)
	}
}

func setResTime(ctx *gin.Context) {
	startAt, ok := ctx.Get("x-start-time-at")
	if !ok {
		return
	}
	// 添加一毫秒的偏差时间
	ctx.Header("X-Response-Time", fmt.Sprintf("%d ms", time.Now().UnixMilli()-startAt.(int64)+1))
}

//func SetCloudFrontCustomDomainCookies(c *gin.Context, respData any) {
//	var signedCookies *filecloudfront.SignedCookies
//	var sok bool
//	signedCookies, sok = respData.(*filecloudfront.SignedCookies)
//	if !sok {
//		cookieInterface, ok := respData.(response.CloudFrontSignedCookiesInterface)
//		if ok {
//			signedCookies = cookieInterface.GetCloudFrontSignedCookies()
//		}
//	}
//
//	if signedCookies != nil {
//		maxAge := int(signedCookies.Duration)
//		mainDomain := strings.Replace(signedCookies.CustomDomain, `file-cdn.`, ``, 1)
//		for _, cookie := range signedCookies.CustomDomainCookies {
//			c.SetCookie(cookie.Name, cookie.Value, maxAge, `/`, mainDomain, cookie.Secure, cookie.HttpOnly)
//		}
//	}
//}

func RemoveCloudFrontCustomDomainCookies(c *gin.Context, domain string) {
	mainDomain := strings.Replace(domain, `file-cdn.`, ``, 1)
	c.SetCookie(sign.CookiePolicyName, ``, -1, `/`, mainDomain, true, true)
	c.SetCookie(sign.CookieKeyIDName, ``, -1, `/`, mainDomain, false, true)
	c.SetCookie(sign.CookieSignatureName, ``, -1, `/`, mainDomain, false, true)
}
