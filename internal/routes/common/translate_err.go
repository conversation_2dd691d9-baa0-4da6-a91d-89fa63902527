package common

import (
	"errors"
	"strings"

	"github.com/gin-gonic/gin/binding"
	"github.com/go-playground/locales/en"
	ut "github.com/go-playground/universal-translator"
	"github.com/go-playground/validator/v10"
	entranslations "github.com/go-playground/validator/v10/translations/en"

	"web-coder-app/pkg/ecode"
)

var (
	uni   *ut.UniversalTranslator
	trans ut.Translator
)

func init() {
	if v, ok := binding.Validator.Engine().(*validator.Validate); ok {
		en2 := en.New()
		uni = ut.New(en2, en2)
		trans, _ = uni.GetTranslator("en")
		err := entranslations.RegisterDefaultTranslations(v, trans)
		if err != nil {
			panic(err)
		}
	}
}

func TranslateErr(err error) string {
	if err == nil {
		return ""
	}
	var errs validator.ValidationErrors
	if !errors.As(err, &errs) {
		return err.Error()
	}
	var builder strings.Builder
	for _, e := range errs {
		switch e.Tag() {
		case "AppNameValidation":
			builder.WriteString("invalid app_name")
		case "chat_models":
			builder.WriteString("invalid chat_models")
		case "chat_model":
			builder.WriteString("invalid model")
		case "chat_from":
			builder.WriteString("invalid from value")
		default:
			if e.Namespace() == "CompletionReq.Prompt" {
				builder.WriteString(ecode.TokenTooLong.Error())
			} else {
				builder.WriteString(e.Translate(trans))
			}
		}
		builder.WriteByte('\n')
	}
	return builder.String()
}
