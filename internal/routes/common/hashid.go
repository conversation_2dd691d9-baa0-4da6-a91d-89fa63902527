package common

import (
	"reflect"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"

	"web-coder-app/internal/clients/hashid"
	"web-coder-app/pkg/ecode"
)

var hashID *hashid.HashID

func SetHashID(h *hashid.HashID) {
	hashID = h
}

func ShouldBindWithHashID(c *gin.Context, obj any, b ...binding.Binding) error {
	if len(b) > 0 {
		if err := c.ShouldBindWith(obj, b[0]); err != nil {
			return err
		}
	} else {
		if err := c.ShouldBind(obj); err != nil {
			return err
		}
	}
	return ParseHashID(obj)
}

func ShouldBindQueryWithHashID(c *gin.Context, obj any) error {
	if err := c.ShouldBindQuery(obj); err != nil {
		return err
	}
	return ParseHashID(obj)
}

func ShouldBindJSONWithHashID(c *gin.Context, obj any) error {
	if err := c.ShouldBindJSON(obj); err != nil {
		return err
	}
	return ParseHashID(obj)
}

func ShouldBindUriWithHashID(c *gin.Context, obj any) error {
	if err := c.ShouldBindUri(obj); err != nil {
		return err
	}
	return ParseHashID(obj)
}

// example: UserHashID string `hashID:"target=UserID,type=User"`
func ParseHashID(obj any) error {
	values := reflect.ValueOf(obj).Elem()

	if err := parseReflectValue(values); err != nil {
		return err
	}

	// 处理嵌入结构
	for i := 0; i < values.NumField(); i++ {
		val := values.Field(i)
		if val.Kind() == reflect.Struct {
			if err := parseReflectValue(val); err != nil {
				return err
			}
		}
	}

	return nil
}

func parseReflectValue(val reflect.Value) error {
	for i := 0; i < val.NumField(); i++ {
		tag := val.Type().Field(i).Tag.Get("hashID")
		if tag == "" {
			continue
		}

		hashStr, ok := val.Field(i).Interface().(string)
		if !ok || hashStr == "" {
			continue
		}

		targetField, targetValue, err := parseHashTag(tag, hashStr)
		if err != nil {
			return err
		}
		if targetField != "" && targetValue != 0 {
			if target := val.FieldByName(targetField); target.CanSet() && target.CanInt() {
				target.SetInt(int64(targetValue))
			}
		}
	}
	return nil
}

func parseHashTag(tag, hashStr string) (targetField string, targetVal int, err error) {
	tags := strings.Split(tag, ",")
	for _, t := range tags {
		items := strings.Split(t, "=")
		if len(items) != 2 {
			continue
		}
		k, v := items[0], items[1]
		switch k {
		case "target":
			targetField = v
		case "type":
			hashCli := hashID.GetHashID(v)
			if hashCli != nil {
				targetVal, err = hashCli.Decode(hashStr)
				if err != nil {
					err = ecode.InvalidHashID.WithCause(err)
					return
				}
			}
		}
	}
	return
}
