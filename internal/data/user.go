package data

import (
	"context"
	"errors"

	"web-coder-app/internal/data/ent"
	"web-coder-app/internal/data/ent/predicate"
	"web-coder-app/internal/data/ent/user"
	userSchema "web-coder-app/internal/data/ent/user"
	"web-coder-app/internal/data/ent/userloginlog"
	"web-coder-app/pkg/ecode"
)

type UserRepo struct {
	*Data
	//oauth *UserOAuthRepo
}

func NewUserRepo(data *Data) *UserRepo {
	return &UserRepo{Data: data}
}

func (repo *UserRepo) Create(ctx context.Context, u *ent.User) (*ent.User, error) {
	return repo.create(ctx, repo.db, u)
}

func (repo *UserRepo) create(ctx context.Context, db *ent.Client, user *ent.User) (*ent.User, error) {
	u, err := db.User.Create().
		SetNickname(user.Nickname).
		SetRegisterType(user.RegisterType).
		SetDeviceID(user.DeviceID).
		SetRegisterIP(user.RegisterIP).
		SetRegisterRegion(user.RegisterRegion).
		SetEmail(user.Email).
		SetEmailVerified(user.EmailVerified).
		SetPassword(user.Password).
		SetRegisterAppName(user.RegisterAppName).
		SetAvatar(user.Avatar).
		SetProfile(user.Profile).
		SetRegisterFrom(user.RegisterFrom).
		SetPhone(user.Phone).
		SetRegisterAppVersion(user.RegisterAppVersion).
		SetExtra(user.Extra).
		Save(ctx)
	if err != nil {
		return nil, errors.Join(err, errors.New("data.user.create"))
	}
	return u, err
}

func (repo *UserRepo) FindByDeviceID(ctx context.Context, deviceID string) (*ent.User, error) {
	u, err := repo.db.User.Query().Where(user.DeviceIDEQ(deviceID)).Only(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, errors.Join(err, errors.New("data.user.findByDeviceID.not_found"))
		}
		return nil, errors.Join(err, errors.New("data.user.findByDeviceID"))
	}
	return u, nil
}

func (repo *UserRepo) FindByID(ctx context.Context, uid int) (*ent.User, error) {
	return repo.findOne(ctx, userSchema.ID(uid))
}

func (repo *UserRepo) findOne(ctx context.Context, ps ...predicate.User) (*ent.User, error) {
	user, err := repo.db.User.Query().
		Where(ps...).
		Where(userSchema.DeleteTimeIsNil()).
		First(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, ecode.NotFound
		}
		return nil, errors.Join(err, errors.New("data.user.findOne"))
	}
	return user, nil
}

func (repo *UserRepo) LoginIsValid(ctx context.Context, tokenID string) error {
	status, err := repo.GetLoginStatus(ctx, tokenID)
	if err != nil {
		if errors.Is(err, ecode.NotFound) {
			//return ecode.JoinStr(ecode.InvalidToken, "tokenID not found")
			return nil
		}
		return ecode.InvalidToken.WithCause(err)
	}
	switch status {
	case userloginlog.StatusExpired:
		//return ecode.LoginExpired
		return ecode.JoinStr(ecode.InvalidToken, "expired")
	case userloginlog.StatusKickAdmin:
		return ecode.LoginKickedByAdmin
	case userloginlog.StatusKickLimit:
		return ecode.LoginKickedByLimit
	case userloginlog.StatusLogout:
		return ecode.AlreadyLogout
	default:
		return nil
	}
}

func (repo *UserRepo) GetLoginStatus(ctx context.Context, tokenID string) (userloginlog.Status, error) {
	log, err := repo.db.UserLoginLog.Query().
		Select(userloginlog.FieldStatus, userloginlog.FieldExpireTime).
		Where(userloginlog.DeleteTimeIsNil()).
		Where(userloginlog.TokenIDEQ(tokenID)).
		First(ctx)
	if err != nil {
		return "", repo.warpError(err)
	}
	if log.Status == userloginlog.StatusValid {
		// 先改为不判断过期
		//if log.ExpireTime != nil && log.ExpireTime.Before(time.Now()) {
		//	return userloginlog.StatusExpired, nil
		//}
	}
	return log.Status, nil
}
