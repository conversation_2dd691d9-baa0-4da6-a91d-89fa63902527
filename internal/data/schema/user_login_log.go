package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"

	"web-coder-app/pkg/entutil"
)

type UserLoginLog struct {
	ent.Schema
}

func (UserLoginLog) Mixin() []ent.Mixin {
	return []ent.Mixin{
		entutil.CreateTime{},
		entutil.DeleteTime{},
	}
}

func (UserLoginLog) Fields() []ent.Field {
	return []ent.Field{
		field.Int("user_id").Positive(),
		field.String("token_id").NotEmpty(),
		field.String("app_name").NotEmpty(),
		field.String("app_version").Optional().Default(""),
		field.String("ip").NotEmpty(),
		field.String("region").Default(""),
		field.String("login_type").NotEmpty(),
		field.String("from").Optional().Default(""),
		field.Time("expire_time").
			Nillable().
			Optional().
			SchemaType(map[string]string{
				dialect.MySQL:    "datetime",
				dialect.Postgres: "timestamp",
			}),
		field.Enum("status").Values("valid", "expired", "logout", "kick_admin", "kick_limit").
			SchemaType(map[string]string{
				dialect.MySQL:    "varchar(32)",
				dialect.Postgres: "varchar(32)",
			}),
		field.JSON("extra", map[string]any{}).Optional(),
	}
}

//func (UserLoginLog) Edges() []ent.Edge {
//	return []ent.Edge{
//		edge.From("user", User.Type).Ref("user_login_log").Unique().Required().Field("user_id"),
//	}
//}

func (u UserLoginLog) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("user_id"),
		index.Fields("token_id").Unique(),
	}
}
