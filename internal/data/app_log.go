package data

import (
	"context"

	"github.com/samber/lo"

	"web-coder-app/internal/data/ent"
)

type AppLogRepo struct {
	*Data
}

func NewServerLogRepo(data *Data) *AppLogRepo {
	return &AppLogRepo{
		Data: data,
	}
}

func (repo *AppLogRepo) create(log *ent.AppLog) *ent.AppLogCreate {
	log.ErrMsg = lo.Substring(log.ErrMsg, 0, 4000)
	log.RespErrMsg = lo.Substring(log.RespErrMsg, 0, 4000)
	creator := repo.db.AppLog.Create().
		SetUserID(log.UserID).SetIP(log.IP).SetMethod(log.Method).
		SetPath(log.Path).SetQuery(log.Query).SetErrMsg(log.ErrMsg).
		SetRespErrMsg(log.RespErrMsg).SetBody(log.Body).SetExtra(log.Extra).
		SetCode(log.Code).SetTraceID(log.TraceID).SetServiceName(log.ServiceName)
	if log.Level != "" {
		creator.SetLevel(log.Level)
	}
	if log.From != "" {
		creator.SetFrom(log.From)
	}
	return creator
}

func (repo *AppLogRepo) Create(ctx context.Context, log *ent.AppLog) (*ent.AppLog, error) {
	return repo.create(log).Save(ctx)
}

func (repo *AppLogRepo) Creates(ctx context.Context, logs ...*ent.AppLog) error {
	creators := make([]*ent.AppLogCreate, 0, len(logs))
	for _, log := range logs {
		creators = append(creators, repo.create(log))
	}
	return repo.db.AppLog.CreateBulk(creators...).Exec(ctx)
}
