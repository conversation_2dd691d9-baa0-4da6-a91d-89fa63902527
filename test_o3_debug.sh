#!/bin/bash

# 调试o3模型推理功能的脚本

# 设置服务器地址
SERVER_URL=${SERVER_URL:-"http://localhost:8082"}

# 检查是否设置了AUTH_TOKEN
if [ -z "$AUTH_TOKEN" ]; then
    echo "请设置AUTH_TOKEN环境变量"
    echo "例如: export AUTH_TOKEN='your-jwt-token'"
    exit 1
fi

echo "调试o3模型推理功能..."
echo "服务器地址: $SERVER_URL"
echo "认证Token: ${AUTH_TOKEN:0:20}..."
echo "=" * 50

# 构建请求JSON
REQUEST_JSON='{
  "model": "o3",
  "prompt": [
    {
      "type": "text", 
      "text": "请逐步计算 15 * 8，并详细解释你的思考过程"
    }
  ],
  "reasoningEffort": "medium",
  "reasoningSummary": "detailed",
  "useResponsesAPI": true,
  "maxTokens": 1000
}'

echo "发送请求..."
echo "请求内容: $REQUEST_JSON"
echo ""

# 发送请求并显示详细的流式响应
curl -N -v \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer $AUTH_TOKEN" \
     -H "Accept: text/event-stream" \
     -d "$REQUEST_JSON" \
     "$SERVER_URL/v1/chat/stream" 2>&1 | while IFS= read -r line; do
    
    # 显示HTTP头信息
    if [[ $line == *"HTTP/"* ]] || [[ $line == *"Content-Type"* ]] || [[ $line == *"Connection"* ]]; then
        echo "[HTTP] $line"
        continue
    fi
    
    # 处理SSE数据
    if [[ $line == data:* ]]; then
        data=${line#data: }
        echo "[RAW] $data"
        
        if [[ $data == "[DONE]" ]]; then
            echo ""
            echo "响应完成"
            break
        elif [[ $data == "[PULSE]" ]]; then
            echo "[HEARTBEAT] 心跳信号"
        else
            # 尝试解析JSON并提取内容
            if command -v jq >/dev/null 2>&1; then
                msg_type=$(echo "$data" | jq -r '.type // "unknown"' 2>/dev/null)
                case $msg_type in
                    "text")
                        content=$(echo "$data" | jq -r '.text // ""' 2>/dev/null)
                        echo "[TEXT] $content"
                        ;;
                    "reasoning")
                        content=$(echo "$data" | jq -r '.text // ""' 2>/dev/null)
                        echo "[REASONING] $content"
                        ;;
                    *)
                        echo "[OTHER] Type: $msg_type, Data: $data"
                        ;;
                esac
            else
                echo "[JSON] $data"
            fi
        fi
    else
        # 其他行（可能是空行或其他SSE格式）
        if [[ -n "$line" ]]; then
            echo "[OTHER] $line"
        fi
    fi
done

echo ""
echo "调试完成"
