#!/bin/bash

# 测试o3模型推理功能的简单脚本

# 设置服务器地址
SERVER_URL=${SERVER_URL:-"http://localhost:8082"}

# 检查是否设置了AUTH_TOKEN
if [ -z "$AUTH_TOKEN" ]; then
    echo "请设置AUTH_TOKEN环境变量"
    echo "例如: export AUTH_TOKEN='your-jwt-token'"
    exit 1
fi

echo "测试o3模型推理功能..."
echo "服务器地址: $SERVER_URL"
echo "=" * 50

# 构建请求JSON
REQUEST_JSON='{
  "model": "o3",
  "prompt": [
    {
      "type": "text", 
      "text": "请逐步计算 15 * 8，并详细解释你的思考过程"
    }
  ],
  "reasoningEffort": "medium",
  "reasoningSummary": "detailed",
  "useResponsesAPI": true,
  "maxTokens": 1000
}'

echo "发送请求..."
echo "请求内容: $REQUEST_JSON"
echo ""

# 发送请求并显示流式响应
curl -N -H "Content-Type: application/json" \
     -H "Authorization: Bearer $AUTH_TOKEN" \
     -H "Accept: text/event-stream" \
     -d "$REQUEST_JSON" \
     "$SERVER_URL/v1/chat/stream" | while IFS= read -r line; do
    if [[ $line == data:* ]]; then
        data=${line#data: }
        if [[ $data == "[DONE]" ]]; then
            echo ""
            echo "响应完成"
            break
        elif [[ $data == "[PULSE]" ]]; then
            echo -n "."
        else
            # 尝试解析JSON并提取内容
            echo "$data" | jq -r 'if .type == "text" then .content elif .type == "reasoning" then "[思考] " + .content else . end' 2>/dev/null || echo "$data"
        fi
    fi
done

echo ""
echo "测试完成"
