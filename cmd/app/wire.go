//go:build wireinject
// +build wireinject

package main

import (
	"context"

	"github.com/google/wire"

	"web-coder-app/configs"
	"web-coder-app/internal/clients"
	"web-coder-app/internal/cmd"
	"web-coder-app/internal/crontab"
	"web-coder-app/internal/data"
	"web-coder-app/internal/routes"
	"web-coder-app/internal/service"
)

func app(ctx context.Context) (*cmd.App, func(), error) {
	panic(wire.Build(
		configs.InitConfig,
		clients.ProviderSet,
		routes.ProviderSet,
		data.ProviderSet,
		service.ProviderSet,
		crontab.ProviderSet,
		cmd.ProviderSet,
	))
}
