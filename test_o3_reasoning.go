package main

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"strings"
	"time"
)

// ChatRequest represents the chat request structure
type ChatRequest struct {
	Model           string          `json:"model"`
	Prompt          []PromptContent `json:"prompt"`
	ReasoningEffort string          `json:"reasoningEffort,omitempty"`
	ReasoningSummary string         `json:"reasoningSummary,omitempty"`
	UseResponsesAPI bool            `json:"useResponsesAPI,omitempty"`
	MaxTokens       *int            `json:"maxTokens,omitempty"`
}

type PromptContent struct {
	Type string `json:"type"`
	Text string `json:"text"`
}

func main() {
	// 从环境变量获取服务器地址和token
	serverURL := os.Getenv("SERVER_URL")
	if serverURL == "" {
		serverURL = "http://localhost:8082"
	}
	
	token := os.Getenv("AUTH_TOKEN")
	if token == "" {
		fmt.Println("请设置AUTH_TOKEN环境变量")
		return
	}

	// 构建测试请求
	req := ChatRequest{
		Model: "o3",
		Prompt: []PromptContent{
			{
				Type: "text",
				Text: "请逐步计算 15 * 8，并详细解释你的思考过程",
			},
		},
		ReasoningEffort:  "medium",
		ReasoningSummary: "detailed",
		UseResponsesAPI:  true,
		MaxTokens:        func() *int { v := 1000; return &v }(),
	}

	// 序列化请求
	reqBody, err := json.Marshal(req)
	if err != nil {
		fmt.Printf("序列化请求失败: %v\n", err)
		return
	}

	// 创建HTTP请求
	httpReq, err := http.NewRequest("POST", serverURL+"/v1/chat/stream", bytes.NewReader(reqBody))
	if err != nil {
		fmt.Printf("创建HTTP请求失败: %v\n", err)
		return
	}

	// 设置请求头
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", "Bearer "+token)
	httpReq.Header.Set("Accept", "text/event-stream")

	// 创建HTTP客户端
	client := &http.Client{
		Timeout: 5 * time.Minute,
	}

	// 发送请求
	fmt.Printf("发送请求到: %s\n", httpReq.URL.String())
	fmt.Printf("请求体: %s\n", string(reqBody))
	fmt.Println("开始接收流式响应...")
	fmt.Println("=" * 50)

	resp, err := client.Do(httpReq)
	if err != nil {
		fmt.Printf("发送请求失败: %v\n", err)
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		fmt.Printf("请求失败，状态码: %d, 响应: %s\n", resp.StatusCode, string(body))
		return
	}

	// 读取流式响应
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	reader := resp.Body
	buffer := make([]byte, 4096)
	var accumulated strings.Builder

	for {
		select {
		case <-ctx.Done():
			fmt.Println("\n请求超时")
			return
		default:
			n, err := reader.Read(buffer)
			if err != nil {
				if err == io.EOF {
					fmt.Println("\n流式响应结束")
					return
				}
				fmt.Printf("\n读取响应失败: %v\n", err)
				return
			}

			chunk := string(buffer[:n])
			accumulated.WriteString(chunk)
			
			// 处理SSE数据
			lines := strings.Split(accumulated.String(), "\n")
			accumulated.Reset()
			
			// 保留最后一行（可能不完整）
			if len(lines) > 0 {
				accumulated.WriteString(lines[len(lines)-1])
				lines = lines[:len(lines)-1]
			}

			for _, line := range lines {
				if strings.HasPrefix(line, "data: ") {
					data := strings.TrimPrefix(line, "data: ")
					if data == "[DONE]" {
						fmt.Println("\n响应完成")
						return
					}
					if data == "[PULSE]" {
						fmt.Print(".")
						continue
					}

					// 解析JSON消息
					var message map[string]interface{}
					if err := json.Unmarshal([]byte(data), &message); err != nil {
						fmt.Printf("\n解析消息失败: %v, 数据: %s\n", err, data)
						continue
					}

					// 处理不同类型的消息
					if msgType, ok := message["type"].(string); ok {
						switch msgType {
						case "text":
							if content, ok := message["content"].(string); ok {
								fmt.Print(content)
							}
						case "reasoning":
							if content, ok := message["content"].(string); ok {
								fmt.Printf("\n[思考过程] %s\n", content)
							}
						default:
							fmt.Printf("\n[%s] %v\n", msgType, message)
						}
					}
				}
			}
		}
	}
}
