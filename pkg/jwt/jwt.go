package jwt

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/golang-jwt/jwt/v5"

	"web-coder-app/pkg/ecode"
)

const (
	bearer = "Bearer"
	space  = " "
)

type ClaimsParam struct {
	UserID       int    `json:"user_id"`
	RegisterType string `json:"register_type"`
	AppName      string `json:"app_name"`
	TokenID      string `json:"token_id"`
}

type Config struct {
	Secret string `yaml:"Secret"`
	Issuer string `yaml:"Issuer"`
}

type Token struct {
	Token    string
	ExpireAt int64
}

type CustomClaims struct {
	ClaimsParam
	jwt.RegisteredClaims
}

type JWT struct {
	secret  []byte
	issuer  string
	subject string
}

func NewJWT(conf *Config) (*JWT, error) {
	if conf == nil || conf.Secret == "" || conf.Issuer == "" {
		return nil, errors.New("jwt config error")
	}
	return &JWT{
		issuer: conf.Issuer,
		secret: []byte(conf.Secret),
	}, nil
}

func (j *JWT) GenerateToken(param ClaimsParam, duration time.Duration) (*Token, error) {
	issuedAt := time.Now()
	expiresAt := issuedAt.Add(duration)
	claims := CustomClaims{
		param,
		jwt.RegisteredClaims{
			Issuer:    j.issuer,
			Subject:   "",
			Audience:  []string{""},
			ExpiresAt: jwt.NewNumericDate(expiresAt),
			NotBefore: jwt.NewNumericDate(issuedAt),
			IssuedAt:  jwt.NewNumericDate(issuedAt),
		},
	}
	withClaims := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	signedString, err := withClaims.SignedString(j.secret)
	if err != nil {
		return nil, err
	}
	return &Token{
		Token:    fmt.Sprintf("%s%s%s", bearer, space, signedString),
		ExpireAt: expiresAt.Unix(),
	}, nil
}

func (j *JWT) ParseToken(input string) (*CustomClaims, error) {
	split := strings.Split(input, space)
	if len(split) != 2 || split[0] != bearer {
		return nil, ecode.InvalidToken.WithCause(fmt.Errorf("ParseToken:token format error"))
	}
	signedString := split[1]

	token, err := jwt.ParseWithClaims(signedString, &CustomClaims{}, func(token *jwt.Token) (interface{}, error) {
		return j.secret, nil
	})
	if err != nil {
		// 兼容前端不能正常刷新token的bug，过期后正常返回
		if errors.Is(err, jwt.ErrTokenExpired) {
			claims, ok := token.Claims.(*CustomClaims)
			if ok {
				return claims, nil
			}
		}
		return nil, ecode.InvalidToken.WithCause(fmt.Errorf("ParseToken:parse err:%s", err))
	}

	claims, ok := token.Claims.(*CustomClaims)
	if !(ok && token.Valid) {
		return nil, ecode.InvalidToken.WithCause(fmt.Errorf("ParseToken:invalid token:%+v", token))
	}

	//if time.Now().Unix() > claims.ExpiresAt.Unix() {
	//	return nil, ecode.ExpiresToken
	//}

	return claims, nil
}
